import { createRouter, createWebHashHistory } from 'vue-router'
interface routerPageInterface {
  path: string
  name: string
  props: any
  component: any
  redirect?: any
  meta?: any
}
const routes: any = [{
  path: '/:id?',
  props: true,
  sensitive: true,
  redirect: { name: 'HomePage' }
}]
const importPages = (modules: Record<string, any>) => {
  for (const path in modules) {
    const pathArr = path.split('/').filter((_f, i) => i > 1)
    const childName = pathArr.join('/').replace('.vue', '')
    const pageName = childName.split('/').join('_')
    // 将匹配 /users, /Users, 以及 /users/42 而非 /users/ 或 /users/42/
    const routePage: routerPageInterface = {
      path: `/${childName}/:id?`,
      name: pageName,
      component: markRaw(modules[path].default),
      meta: {
        name: pageName[pageName.length - 1]
      },
      props: (route: any) => (route?.query ? { ...route.query } : null)
      // component: () => modules[path] // 有警告 不建议用
      // component: () => import(path) // 相当于又引入一遍 也不建议用
    }

    routes.push(routePage)
  }
}
console.log(routes)
importPages(import.meta.glob('../views/**/*.vue', { eager: true }))
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach((to, from) => {
  // 如果访问根路径，重定向到 HomePage
  if (to.fullPath === '/' && from.fullPath !== '/') {
    return { name: 'HomePage' }
  }
})

export default router
