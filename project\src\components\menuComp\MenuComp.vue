<script setup lang='ts'>
defineOptions({ name: 'MenuComp' })

// import { useRouter } from 'vue-router'
// const router = useRouter()

const props = withDefaults(
  defineProps<{
    menuList: any
  }>(), {}
)

import { projectStore } from '../../store'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { currentLeftMenu, pageJump } = storeToRefs(store)

watch(() => props.menuList, (val: any[]) => {
  emit('leftMenuClick')
  if (Array.isArray(val) && val.length > 0) {
    if (!pageJump.value){
      const exist = val.find((item: any) => item.menuId === currentLeftMenu.value?.menuId)
      if (!exist) {
        store.setCurrentLeftMenu(val[0])
      }
    }
    emit('update:thirdMenuList', currentLeftMenu.value?.children || [])
  } else {
    emit('update:thirdMenuList', [])
  }
  // if (!pageJump.value) router.push('/')
}, { deep: true }
)

const emit = defineEmits(['update:thirdMenuList', 'leftMenuClick'])

// const currentMenu = ref()
const menuClick = (item: any) => {
  // if (!pageJump.value) router.push('/')
  store.setPageJump(false)
  store.setCurrentLeftMenu(item)
  emit('update:thirdMenuList', item?.children || [])
  emit('leftMenuClick')

  window.sessionStorage.removeItem('searchData')
}

</script>

<template>
  <el-scrollbar height="100%">
    <div class="left-menu">
      <div class="top-bg"/>
      <div class="center-bg"/>
      <div class="bottom-bg"/>
      <div class="menu-list" v-if="menuList && menuList.length>0">
        <div class="menu-item" v-for="item in menuList" :key="item.menuId" @click="menuClick(item)" :class="{active: currentLeftMenu?.menuId === item.menuId}">
          <img class="menu-icon" v-if="currentLeftMenu?.menuId === item.menuId" src="../../assets/images/left-menu-icon-active.png" alt="">
          <img class="menu-icon" v-else src="../../assets/images/left-menu-icon.png" alt="">
          <p>{{ item.menuName }}</p>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<style lang='scss' scoped>
.el-scrollbar{
  :deep{
    .el-scrollbar__wrap{
      height: 100%;
      .el-scrollbar__view{
        height: 100%;
      }
    }
  }
}
.left-menu{
  width: 270px;
  height: 100%;
  min-height: 650px;
  position: relative;
  overflow: hidden;
  padding-left: 4px;
  .top-bg{
    position: absolute;
    top: 0;
    left: 4px;
    right: 0;
    height: 296px;
    background: url(../../assets/images/menu-top-bg.png) no-repeat center center;
    background-size: 100% 100%;
  }
  .center-bg{
    position: absolute;
    top: 296px;
    bottom: 350px;
    left: 4px;
    right: 0;
    background: url(../../assets/images/menu-center-bg.png) repeat center center;
    background-size: 100% 100%;
  }
  .bottom-bg{
    position: absolute;
    bottom: 0;
    left: 4px;
    right: 0;
    height: 350px;
    background: url(../../assets/images/menu-bottom-bg.png) no-repeat center center;
    background-size: 100% 100%;
  }
  .menu-list{
    position: relative;
    margin-top: 105px;
    width: 100%;
    padding-right: 20px;
    height: calc(100% - 110px);
    overflow-y: auto;
    .menu-item{
      margin-left: -4px;
      font-size: 16px;
      color: #484A50;
      margin-top: 35px;
      padding: 15px 25px 15px 36px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
      position: relative;
      img.menu-icon{
        width: 20px;
        height: 20px;
      }
      &:first-child{
        margin-top: 0;
      }
      &.active{
        background: #2F6BFF;
        border-radius: 6px;
        color: #fff;
        &::after{
          content: '';
          position: absolute;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #3DD9FF;
          top: 50%;
          transform: translateY(-50%);
          right: 20px;
        }
      }
    }
    /* 针对原生滚动条（兼容性好） */
    &::-webkit-scrollbar {
      width: 4px;
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-button {
      display: none;
      height: 0;
      width: 0;
    }
  }
}
</style>
