<script setup lang="ts">
defineOptions({ name: 'DistrictInvestigateHandle' })
import { downLoadFile } from '@uq/base/hooks'

const props = defineProps({
  initData: {
    type: Object,
    default: () => {}
  }
})

const myForm = ref()

type SimpleObject = Record<string, any>;
const formData = ref<SimpleObject>(props.initData)

formData.value.caseOrderCorrectVO.correctType = formData.value.caseOrderCorrectVO.correctType ? formData.value.caseOrderCorrectVO.correctType.split(',') : ['zg']
formData.value.caseOrderCorrectVO.reviewCorrectConclusion = formData.value.caseOrderCorrectVO.reviewCorrectConclusion ? formData.value.caseOrderCorrectVO.reviewCorrectConclusion.split(',') : []
formData.value.caseOrderCorrectVO.pleadFlag = formData.value.caseOrderCorrectVO.pleadFlag ? formData.value.caseOrderCorrectVO.pleadFlag.split(',') : []
formData.value.caseOrderCorrectVO.hearingFlag = formData.value.caseOrderCorrectVO.hearingFlag ? formData.value.caseOrderCorrectVO.hearingFlag.split(',') : []

formData.value.handleTypeList.forEach((item: any) => {
  item.disabled = !formData.value.handleTypeFlag
})
const zgcljlList = [
  { dmName: '已按要求完成责改，结案', dmValue: '1' },
]

const dsrList = [
  { dmName: '当事人陈述、申辩', dmValue: '1' },
]
const tzList = [
  { dmName: '听证', dmValue: '1' },
]

// 获取执法文书的上面一排数据
import { projectStore } from '../../../store'
const store = projectStore()
let enforcementDocuments: any[] = []

watch(() => formData.value.handleType, (val) => {
  if (val === '01'){ //案件移送
    enforcementDocuments = props.initData.tabMap['transfer']
  } else if (val === '02'){ //撤销立案
    enforcementDocuments = props.initData.tabMap['revoke']
  } else if (val === '03'){ //责令改正
    enforcementDocuments = props.initData.tabMap['correct']
  } else if (val === '04'){ //行政指导
    enforcementDocuments = props.initData.tabMap['guide']
  }

  store.setEnforcementDocuments(enforcementDocuments)
}, {
  immediate: true
})
watch(() => formData.value.caseOrderCorrectVO.hearingFlag, (val) => {
  if (formData.value.handleType === '03'){
    if (val.indexOf('1') > -1) {
      enforcementDocuments = (enforcementDocuments || []).concat((props.initData.tabMap['hear'] || []))
    } else {
      enforcementDocuments = props.initData.tabMap['correct']
    }
  }

  store.setEnforcementDocuments(enforcementDocuments)
}, {
  immediate: true
})

// 表单校验
const validateForm = async (): Promise<boolean> => {
  try {
    await myForm.value?.validate()
    return true
  } catch {
    return false
  }
}
// 暴露数据获取方法
const getFormData = () => ({ ...formData })
// 暴露给父组件的方法清单
defineExpose({
  validateForm,
  getFormData
})
// 预览文件
interface activeFileInterface {
  docName: string
  docId: string
  ext: string
}
const activeFile: activeFileInterface = reactive(<activeFileInterface>{})
const previewShow = ref(false)
const tableRowClick = (btn: any, row: any) => {
  if (btn === 'preview') {
    previewShow.value = true
    activeFile.docName = row.docName
    activeFile.docId = row.docId
    activeFile.ext = ['jpg', 'png', 'jpeg', 'gif'].indexOf(row.docExt) > -1 ? 'image' : 'pdf'
  } else if (btn === 'download') {
    downLoadFile(row.docId)
  }
}
</script>
<!-- 办理信息 -->
<template>
  <el-form class="uq-form" :model="formData" ref="myForm" size="large" label-position="right" label-width="140px" :disabled="!formData.formRight">
    <!-- PunishCity/AdministrativePenalty -->
    <el-form>
      <uq-radio label="" v-model="formData['handleType']" prop="handleType" :options="formData.handleTypeList" label-width="0"  rules="required" />
    </el-form>
    <!-- 案件移送 -->
    <template v-if="formData.handleType === '01'">
      <div class="model-title mb-20">案件移送信息</div>
      <el-row>
        <el-col :span="8">
          <uq-input v-model="formData.caseTransferVO['transferOrgan']" prop="caseTransferVO.transferOrgan" label="移送机关" placeholder="请输入移送机关" rules="required" />
        </el-col>
        <el-col :span="8">
          <uq-date v-model="formData.caseTransferVO['transferTime']" prop="caseTransferVO.transferTime" label="移送时间" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择移送时间" rules="required" />
        </el-col>
        <el-col :span="24">
          <uq-input v-model="formData.caseTransferVO['transferReason']" prop="caseTransferVO.transferReason" type="textarea" label="移送理由" placeholder="请输入移送理由" rules="required" />
        </el-col>
      </el-row>
      <el-form-item label="上传附件">
        <chunk-uploader v-model="formData.caseAdministrativeGuidanceVO['caseFileList']" :fileParams="{instanceId:formData.id, docType:'xzzd',docUser:'uqian',docDir:'hlwjjg/punish'}" style="width: 100%;" />
      </el-form-item>
    </template>
    <!-- 撤销立案 -->
    <template v-if="formData.handleType === '02'">
      <div class="model-title mb-20">案件移送信息</div>
      <el-row>
        <el-col :span="8">
          <uq-date v-model="formData.caseRevokeVO['revokeTime']" prop="caseRevokeVO.revokeTime" label="撤销日期" placeholder="请选择撤销日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" rules="required" />
        </el-col>
        <el-col :span="24">
          <uq-input v-model="formData.caseRevokeVO['revokeReason']" prop="caseRevokeVO.revokeReason"  label="撤销理由" placeholder="请输入撤销理由" type="textarea"  rules="required" />
        </el-col>
      </el-row>
      <el-form-item label="上传附件">
        <chunk-uploader v-model="formData.caseAdministrativeGuidanceVO['caseFileList']" :fileParams="{instanceId:formData.id, docType:'xzzd',docUser:'uqian',docDir:'hlwjjg/punish'}" style="width: 100%;" />
      </el-form-item>
    </template>
    <!-- 责令改正 -->
    <template v-if="formData.handleType === '03'">
      <el-form-item label="责改情况">
        <el-checkbox-group v-model="formData.caseOrderCorrectVO['correctType']">
          <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in formData.correctTypeList" :disabled="item.dmValue === 'zg'" :key="index" />
        </el-checkbox-group>
      </el-form-item>
      <el-row>
        <el-col :span="6">
          <uq-date v-model="formData.caseOrderCorrectVO['correctStartDate']" prop="caseOrderCorrectVO.correctStartDate" label="开始日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择开始日期" rules="required"/>
        </el-col>
        <el-col :span="6">
          <uq-date v-model="formData.caseOrderCorrectVO['correctEndDate']" prop="caseOrderCorrectVO.correctEndDate" label="结束日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择结束日期" rules="required"/>
        </el-col>
        <el-col :span="6">
          <div class="d-flex">
            共 <uq-input v-model="formData.caseOrderCorrectVO['correctDays']" prop="caseOrderCorrectVO.correctDays" label-width="0" rules="dNumeric"/> 天
          </div>
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.caseOrderCorrectVO['correctContent']" prop="caseOrderCorrectVO.correctContent" label="责改内容" placeholder="请输入责改内容" rules="required" />
        </el-col>
      </el-row>
      <div class="tips">
        <p class="bold">责改延期申请</p><p class="danger">（企业端网上大厅发起责改延期申请后，自动补充批复内容）</p>
      </div>
      <el-table :data="formData.caseOrderCorrectVO['delayApplyVOList']" class="mt-10 mb-10">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="noticeDocNum" label="责令改正通知书及编号" align="center" />
        <el-table-column prop="correctContent" label="责令改造期限及内容" align="center" />
        <el-table-column prop="reason" label="申请延期理由" width="150" align="center" />
        <el-table-column prop="delayEndDate" label="申请延期至时间" width="150" align="center" />
        <el-table-column prop="applyPerson" label="申请人/联系电话" width="200" align="center" show-overflow-tooltip>
          <template #default="scope">
            <span>{{scope.row.applyPerson}}</span>/<span>{{scope.row.applyPersonPhone}}</span>
          </template>
        </el-table-column>>
        <el-table-column prop="applyDate" label="申请时间" width="120" align="center" />
        <el-table-column prop="approveResult" label="批复结果" width="120" align="center" />
        <el-table-column prop="approveDate" label="批复时间" width="120" align="center" />
      </el-table>
      <div class="tips mb-10">
        <p class="bold">责改复查信息</p>
        <el-button class="ml-10" size="small" type="primary">责改复查</el-button>
      </div>
      <el-row>
        <el-col :span="12">
          <uq-input v-model="formData.caseOrderCorrectVO['checkNum']" label="责改复查检查单号" placeholder="请输入责改复查检查单号" label-width="140px" disabled />
        </el-col>
        <el-col :span="24">
          <uq-radio label="责改复查结果" v-model="formData.caseOrderCorrectVO['reviewResult']" label-width="140px" :options="formData.reviewResultList"/>
        </el-col>
        <el-col :span="24">
          <uq-input v-model="formData.caseOrderCorrectVO['reviewCorrectCompletion']" label-width="140px" :label="formData.caseOrderCorrectVO['reviewResult'] === '1' ? '责改完成情况' : '责改未完成处理意见'" :placeholder="'请输入' + (formData.caseOrderCorrectVO['reviewResult'] === '1' ? '责改完成情况' : '责改未完成处理意见')" type="textarea" />
        </el-col>
        <el-col :span="24">
          <el-form-item label="责改处理结论">
            <el-checkbox-group v-model="formData.caseOrderCorrectVO['reviewCorrectConclusion']" :disabled="formData.caseOrderCorrectVO['reviewResult'] === '0' || formData.caseOrderCorrectVO['correctType'].indexOf('cf') > -1">
              <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in zgcljlList" :key="index" />
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="model-title mb-20">行政处罚事先告知</div>
      <el-row>
        <el-col :span="6">
          <uq-date v-model="formData.caseOrderCorrectVO['investigationDate']" label="案件调查终结日期" placeholder="请选择案件调查终结日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" label-width="140px"  />
        </el-col>
        <el-col :span="8">
          <uq-date v-model="formData.caseOrderCorrectVO['punishmentDate']" label="行政处罚事先告知书送达日期" placeholder="请选择行政处罚事先告知书送达日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD"  label-width="220px"  />
        </el-col>

        <el-col :span="24"  style="display: flex; align-items: center;">
          <el-form-item label-width="20px" style="width: 180px;">
            <el-checkbox-group v-model="formData.caseOrderCorrectVO['pleadFlag']" >
              <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in dsrList" :key="index" />
            </el-checkbox-group>
          </el-form-item>
          <uq-date v-if="formData.caseOrderCorrectVO['pleadFlag']?.indexOf('1') > -1" v-model="formData.caseOrderCorrectVO['pleadDate']" label="申辩日期" label-width="100px" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择申辩日期" style="width: 300px;" />
        </el-col>

        <el-col :span="24"  style="display: flex; align-items: center;">
          <el-form-item label-width="20px" style="width: 180px;">
            <el-checkbox-group v-model="formData.caseOrderCorrectVO['hearingFlag']" >
              <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in tzList" :key="index" />
            </el-checkbox-group>
          </el-form-item>
          <template v-if="formData.caseOrderCorrectVO['hearingFlag']?.indexOf('1') > -1">
            <uq-date  v-model="formData.caseOrderCorrectVO['hearingNoticeDate']" label="听证通知日期" label-width="100px" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择听证通知日期" style="width: 300px;" />
            <uq-date v-model="formData.caseOrderCorrectVO['hearingPublicationDate']" label="听证公告日期" label-width="120px" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择听证公告日期" style="width: 300px;" />
            <uq-date  v-model="formData.caseOrderCorrectVO['hearingDate']" label="听证日期" label-width="100px" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择听证日期" style="width: 300px;" />
          </template>

        </el-col>
      </el-row>
    </template>
    <!-- 行政指导 -->
    <template v-if="formData.handleType === '04'">
      <uq-radio label="行政指导方式" v-model="formData.caseAdministrativeGuidanceVO['guidanceType']" prop="caseAdministrativeGuidanceVO.guidanceType" :options="formData.guidanceTypeList" rules="required"/>
      <template v-if="formData.caseAdministrativeGuidanceVO.guidanceType === '01'">
        <!-- 下发行政指导意见书 -->
        <uq-date label-width="200px" v-model="formData.caseAdministrativeGuidanceVO['processTime']" prop="caseAdministrativeGuidanceVO.processTime" label="下发行政指导意见书日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择下发行政指导意见书日期" rules="required" style="width: 500px;" />
      </template>
      <template v-if="formData.caseAdministrativeGuidanceVO.guidanceType === '02'">
        <el-row>
          <el-col :span="8">
            <uq-date  v-model="formData.caseAdministrativeGuidanceVO['processTime']" prop="caseAdministrativeGuidanceVO.processTime" label="提醒日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择提醒日期" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processPeople']" prop="caseAdministrativeGuidanceVO.processPeople" label="提醒对象" placeholder="请输入提醒对象" label-width="140px" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processPhone']" prop="caseAdministrativeGuidanceVO.processPhone" label="提醒对象联系方式" placeholder="请输入提醒对象联系方式" rules="required|isPhoneMobileZhbg"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processContent']" prop="caseAdministrativeGuidanceVO.processContent" label="提醒内容" placeholder="请输入提醒内容" type="textarea" rules="required"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['otherPeople']" label="其他参与人" prop="caseAdministrativeGuidanceVO.otherPeople" placeholder="请输入其他参与人"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['operatorName']" prop="caseAdministrativeGuidanceVO.operatorName" label="经办人" placeholder="请输入经办人" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input   v-model="formData.caseAdministrativeGuidanceVO['operatorPhone']" prop="caseAdministrativeGuidanceVO.operatorPhone" label="经办人联系方式" placeholder="请输入经办人联系方式" label-width="140px" rules="required|isPhoneMobileZhbg"  />
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件">
              <chunk-uploader v-model="formData.caseAdministrativeGuidanceVO['caseFileList']" :fileParams="{instanceId:formData.id, docType:'xzzd',docUser:'uqian',docDir:'hlwjjg/punish'}" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="formData.caseAdministrativeGuidanceVO.guidanceType === '03'">
        <el-row>
          <el-col :span="8">
            <uq-date  v-model="formData.caseAdministrativeGuidanceVO['processTime']" prop="caseAdministrativeGuidanceVO.processTime" label="约谈日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择约谈日期" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processPeople']" prop="caseAdministrativeGuidanceVO.processPeople" label="约谈对象" placeholder="请输入约谈对象" label-width="140px" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processPhone']" prop="caseAdministrativeGuidanceVO.processPhone" label="约谈对象联系方式" placeholder="请输入约谈对象联系方式" rules="required|isPhoneMobileZhbg"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processContent']" prop="caseAdministrativeGuidanceVO.processContent" label="约谈内容" placeholder="请输入约谈内容" type="textarea" rules="required"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['otherPeople']" label="其他参与人" prop="caseAdministrativeGuidanceVO.otherPeople" placeholder="请输入其他参与人"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['operatorName']" prop="caseAdministrativeGuidanceVO.operatorName" label="经办人" placeholder="请输入经办人" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['operatorPhone']" prop="caseAdministrativeGuidanceVO.operatorPhone" label="经办人联系方式" placeholder="请输入经办人联系方式" label-width="140px" rules="required|isPhoneMobileZhbg"  />
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件">
              <chunk-uploader style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="formData.caseAdministrativeGuidanceVO.guidanceType === '04'">
        <el-row>
          <el-col :span="8">
            <uq-date  v-model="formData.caseAdministrativeGuidanceVO['processTime']" prop="caseAdministrativeGuidanceVO.processTime" label="处理日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder="请选择处理日期" rules="required"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['processContent']" prop="caseAdministrativeGuidanceVO.processContent" label="处理内容" placeholder="请输入处理内容" type="textarea" rules="required"  />
          </el-col>
          <el-col :span="24">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['otherPeople']" prop="caseAdministrativeGuidanceVO.otherPeople" label="其他参与人" placeholder="请输入其他参与人" />
          </el-col>
          <el-col :span="8">
            <uq-input  v-model="formData.caseAdministrativeGuidanceVO['operatorName']" prop="caseAdministrativeGuidanceVO.operatorName" label="经办人" placeholder="请输入经办人" rules="required"  />
          </el-col>
          <el-col :span="8">
            <uq-input   v-model="formData.caseAdministrativeGuidanceVO['operatorPhone']" prop="caseAdministrativeGuidanceVO.operatorPhone" label="经办人联系方式" placeholder="请输入经办人联系方式" label-width="140px" rules="required|isPhoneMobileZhbg"  />
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传附件">
              <chunk-uploader style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </template>
    <div class="model-title mb-20">外发通知企业接收情况</div>
    <el-table :data="formData['caseEnterpriseNoticeVOList']">
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="templateType" label="类型" align="center" />
      <el-table-column prop="docNum" label="文号" align="center" />
      <el-table-column prop="sendTime" label="发送时间" width="150" align="center" />
      <el-table-column prop="sendName" label="发送人" width="120" align="center" />
      <el-table-column prop="confirmTime" label="企业确认时间" width="150" align="center" />
      <el-table-column prop="confirmType" label="确认方式" width="120" align="center" />
      <el-table-column prop="replyContent" label="反馈说明" width="120" align="center" />
      <el-table-column prop="" label="反馈文件" width="200" align="center">
        <template #default="scope">
          <el-popover  placement="top" :width="130" popper-class="popover" v-for="(item,index) in scope.row?.caseFileList" :key="index">
            <el-button size="small" type="primary" @click="tableRowClick('download',item)">下载</el-button>
            <el-button size="small" type="primary" @click="tableRowClick('preview',item)">预览</el-button>
            <template #reference>
              <span class="primary-btn" :title="item.docName">{{item.docName}}</span>
            </template>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <!-- 预览文件 -->
  <uq-dialog v-model="previewShow" :title="`${activeFile?.docName}`" :fullscreen="true" style="z-index: 99999;">
    <PreviewFilePage v-if="previewShow" :doc-id="activeFile.docId" :ext="activeFile.ext" />
  </uq-dialog>
</template>

<style lang="scss" scoped>
.d-flex{
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 10px;
  .el-form-item{
    width: 100px;
    margin-bottom: 0 !important;
  }
}
.tips{
  padding: 5px 10px;
  background-color: #FBE2E5;
  font-size: 16px;
  p{
    display: inline-block;
  }
  p.bold{
    font-size: bold;
  }
  p.danger{
    color: #D9001B;

  }
}
</style>
