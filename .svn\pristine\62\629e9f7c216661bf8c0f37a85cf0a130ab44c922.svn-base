<script setup lang="ts">
defineOptions({ name: 'Statistic<PERSON>earn' })
import { ref, onMounted } from 'vue'

import { statisticLearn } from '../../api/analysisChart.ts'
const row = ref({
  'signCount': 1,
  'noSignCount': 0,
  'noStartCount': 0,
  'progressCount': 0,
  'finishCount': 1
})
// 学时查询统计
const getstatisticLearn = async () => {
  const res = await statisticLearn()
  row.value = res
}
onMounted(async () => {
  await getstatisticLearn()
})
</script>
<!-- 学时查询统计 -->
<template>
  <div class="Wrap">
    <el-card  class="status-card">
      <div class="card-content">
        <div class="name">已签到</div>
        <div class="value">{{ row.signCount }}人</div>
      </div>
    </el-card>
    <el-card  class="status-card">
      <div class="card-content">
        <div class="name">未签到</div>
        <div class="value">{{ row.noSignCount }}人</div>
      </div>
    </el-card>
    <el-card  class="status-card">
      <div class="card-content">
        <div class="name">未开始</div>
        <div class="value">{{ row.noStartCount }}人</div>
      </div>
    </el-card>
    <el-card  class="status-card">
      <div class="card-content">
        <div class="name">进行中</div>
        <div class="value">{{ row.progressCount }}人</div>
      </div>
    </el-card>
    <el-card  class="status-card">
      <div class="card-content">
        <div class="name">已完成</div>
        <div class="value">{{ row.finishCount }}人</div>
      </div>
    </el-card>

  </div>
  <common-form code="QueryStatistics_FORM" />
</template>

<style lang="scss" scoped>
.Wrap {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  .status-card {
    flex: 1;
    padding: 20px;
    display: flex;
    ::v-deep(.el-card__body){
      width: 100%;
      padding: 0 10px;
    }
    .card-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
