<script setup lang="ts">
import { uqConfirm } from '@uq/base/hooks'
defineOptions({ name: 'HandlingInformationSH' })
import LegalReview from './LegalReview.vue'
import SignIn from '../DocumentForm/SignIn.vue' // 签报
import Meeting from '../DocumentForm/Meeting.vue' // 上会
import DocumentSend from '../DocumentForm/DocumentSend.vue' // 发文
import { fawen, qianbao } from '../../../api/administrativePenalty'
import { ref, watch } from 'vue'

const props = defineProps({
  initData: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['refresh', 'save'])
const myForm = ref()
type SimpleObject = Record<string, any>
const formData = ref<SimpleObject>({})

// 只在 props.initData 引用变化时初始化 formData
watch(
  () => props.initData,
  (newVal) => {
    if (!newVal) return
    // 深拷贝，避免响应式污染
    formData.value = JSON.parse(JSON.stringify(newVal))

    formData.value.casePunishApproveDetailVOList?.forEach((item: any) => {
      item.checkFlag = (item.checkFlag === '1' || item.checkFlag === 1) ? item.checkFlag.split(',') : []
    })
    formData.value.ffsdcwFileList = formData.value.caseFileList?.filter((item: any) => item.docType === 'ffsdcw') || []
    formData.value.xzcfFileList = formData.value.caseFileList?.filter((item: any) => item.docType === 'xzcf') || []
    formData.value.bycfspdFileList = formData.value.caseFileList?.filter((item: any) => item.docType === 'bycfspd') || []
    formData.value.cfspdFileList = formData.value.caseFileList?.filter((item: any) => item.docType === 'cfspd') || []

    if (formData.value.casePunishApproveVO) {
      formData.value.casePunishApproveVO.decisionFlag = formData.value.casePunishApproveVO.decisionFlag ? formData.value.casePunishApproveVO.decisionFlag.split(',') : []
      formData.value.casePunishApproveVO.collectiveDiscussionFlag = formData.value.casePunishApproveVO.collectiveDiscussionFlag ? formData.value.casePunishApproveVO.collectiveDiscussionFlag.split(',') : []
      formData.value.casePunishApproveVO.punishmentFlag = formData.value.casePunishApproveVO.punishmentFlag ? formData.value.casePunishApproveVO.punishmentFlag.split(',') : []
    }
  },
  { immediate: true } // 只监听引用变化
)

// 表单校验
const validateForm = async (): Promise<boolean> => {
  try {
    await myForm.value?.validate()
    return true
  } catch {
    return false
  }
}
// 暴露数据获取方法
const getFormData = () => ({ ...formData })

// 是否重大执法决定字典
const majorDecisionList = [
  {
    dmName: '提请重大执法决定法制审核',
    dmValue: '1'
  }
]
// 集体讨论字典
const groupDiscussionList = [
  {
    dmName: '启动集体讨论',
    dmValue: '1'
  }
]
// 出发内容字典
const departContentList = [
  {
    dmName: '不予行政处罚',
    dmValue: '1'
  }
]

const punishDetailChange = (value: string, row: any) => {
  row.punishDetailMerge = value
  const item = row.punishDetailList.find((item: any) => item.punishDetailMerge === value)
  row.punishCategory = item.punishCategory
  row.punishCategoryName = item.punishCategoryName
  row.punishCode = item.punishCode
  row.punishDetail = item.punishDetail
  row.punishPeriod = item.punishPeriod
  row.punishStandard = item.punishStandard
}

// 法制审核
const showLegalReview = ref(false)
const fzshClick = () => {
  showLegalReview.value = true
}

// 签报
const showSignIn = ref(false)
const qianbaoClick = () => {
  if (formData.value.casePunishApproveVO.signInFlag === '1'){
    showSignIn.value = true
  } else {
    uqConfirm('是否确认签报？', () => {
      qianbao({
        data: formData.value.casePunishApproveVO.caseId
      }).then(() => {
        emit('refresh')
        ElMessage.success('签报成功！')
      })
    })
  }
}
const qianbaoFun = () => {
  uqConfirm('是否确认签报？', () => {
    qianbao({
      ...formData.value.casePunishApproveVO,
      decisionFlag: formData.value.casePunishApproveVO.decisionFlag?.join(','),
      collectiveDiscussionFlag: formData.value.casePunishApproveVO.collectiveDiscussionFlag?.join(','),
      punishmentFlag: formData.value.casePunishApproveVO.punishmentFlag?.join(','),
    }).then(() => {
      emit('refresh')
      ElMessage.success('签报成功！')
    })
  })
}

// 上会
const showMeeting = ref(false)
const shanghuiClick = () => {
  showMeeting.value = true
}
// 发文
const showDocumentSend = ref(false)
const fawenClick = () => {
  showDocumentSend.value = true
  // emit('save', 'fawen')
}
const fawenFun = () => {
  uqConfirm('是否确认发文？', () => {
    fawen({
      ...formData.value.casePunishApproveVO,
      decisionFlag: formData.value.casePunishApproveVO.decisionFlag?.join(','),
      collectiveDiscussionFlag: formData.value.casePunishApproveVO.collectiveDiscussionFlag?.join(','),
      punishmentFlag: formData.value.casePunishApproveVO.punishmentFlag?.join(','),
    }).then(() => {
      emit('refresh')
      ElMessage.success('发文成功！')
    })
  })
}

// 暴露给父组件的方法清单
defineExpose({
  validateForm,
  getFormData,
  fawenFun,
  qianbaoFun
})
</script>
<!-- 办理信息 -->
<template>
  <el-form class="uq-form" :model="formData" ref="myForm" size="large" label-position="right" label-width="180px" :disabled="!formData.formRight">
    <template v-if="formData.from === 'city'">
      <div class="m-title">责改情况</div>
      <el-row class="mt-20">
        <el-col :span="6">
          <uq-date v-model="formData.caseOrderCorrectVO['correctStartDate']" label="开始日期" placeholder="请选择开始日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" disabled />
        </el-col>
        <el-col :span="6">
          <uq-date v-model="formData.caseOrderCorrectVO['correctEndDate']" label="结束日期" placeholder="请选择结束日期" value-format="YYYY-MM-DD" format="YYYY-MM-DD" disabled />
        </el-col>
        <el-col :span="6">
          <div class="ml-20 correctDays">共 {{ formData.caseOrderCorrectVO['correctDays'] }} 天</div>
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.caseOrderCorrectVO['correctContent']" label="责改内容" prop="caseOrderCorrectVO.correctContent" disabled />
          <uq-input v-model="formData.caseOrderCorrectVO['checkNum']" label="责改复查检查单号" prop="caseOrderCorrectVO.checkNum" disabled />
        </el-col>
      </el-row>
    </template>
    <div class="m-title">
      <span class="mr-20">案件审核</span>
      <template v-if="formData.from === 'city'">
        <el-form>
          <el-button size="small" type="primary" :disabled="!formData.casePunishApproveVO['decisionFlag'].includes('1')" @click="fzshClick">法制审核</el-button>
          <el-button size="small" type="primary"  @click="qianbaoClick" :disabled="!formData.casePunishApproveVO['collectiveDiscussionFlag'].includes('1')">签报</el-button>
          <el-button size="small" type="primary" @click="shanghuiClick" :disabled="!formData.casePunishApproveVO['collectiveDiscussionFlag'].includes('1')">上会</el-button>
          <el-button size="small" type="primary" @click="fawenClick">发文</el-button>
        </el-form>
      </template>
      <span class="danger ml-20">处罚决定提醒（当前日期-立案日期={{formData.casePunishApproveVO.decisionWarnDays}}天）</span>
    </div>
    <uq-radio label="是否存在从轻从重情节" v-model="formData.casePunishApproveVO['plotFlag']" prop="casePunishApproveVO.plotFlag" :options="formData.plotList" rules="required" />
    <el-form-item label="裁量基准">
      <el-table :data="formData['casePowerList']">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="punishName" label="违法行为" align="center" />
        <el-table-column prop="punishDetail" label="具体违法情节" align="center" show-overflow-tooltip>
          <template #default="scope">
            <uq-select label="" v-model="scope.row.punishDetailMerge" prop="row.punishDetailMerge" :options="scope.row.punishDetailMergeList" @change="(v: string) => punishDetailChange(v, scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="punishStandard" label="处罚裁量基准" width="160" align="center" show-overflow-tooltip />
        <el-table-column prop="punishPeriod" label="最长公示期限(月)" width="150" align="center" />
        <el-table-column prop="punishCategoryName" label="违法行为分类" width="120" align="center" />
        <el-table-column prop="powerCode" label="职权编号" width="120" align="center" />
      </el-table>
    </el-form-item>
    <el-form-item label="处罚依据信息">
      <el-table :data="formData['illegalGistList']">
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="punishName" label="违法行为" align="center" />
        <el-table-column prop="lawsName" label="法律名称" align="center" show-overflow-tooltip />
        <el-table-column prop="gistTypeText" label="依据分类" width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="stripText" label="条" width="80" align="center" />
        <el-table-column prop="fundText" label="款" width="80" align="center" />
        <el-table-column prop="itemText" label="项" width="80" align="center" />
        <el-table-column prop="content" label="内容" align="center" show-overflow-tooltip />
      </el-table>
    </el-form-item>
    <template v-if="formData.from === 'city'">
      <el-form-item label="是否重大执法决定">
        <el-checkbox-group v-model="formData.casePunishApproveVO['decisionFlag']" style="width: 200px">
          <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in majorDecisionList" :key="index" />
        </el-checkbox-group>
        <el-row style="width: calc(100% - 200px)" v-if="formData.casePunishApproveVO['decisionFlag'].includes('1')">
          <el-col :span="8">
            <uq-date v-model="formData.casePunishApproveVO['legalExamineDate']" label="法制审查日期" placeholder="请选择" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData.casePunishApproveVO['legalExamineResult']" label="法制审核结果" />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="集体讨论">
        <el-checkbox-group v-model="formData.casePunishApproveVO['collectiveDiscussionFlag']" style="width: 200px">
          <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in groupDiscussionList" :key="index" />
        </el-checkbox-group>
        <el-row style="width: calc(100% - 200px)" v-if="formData.casePunishApproveVO['collectiveDiscussionFlag'].includes('1')">
          <el-col :span="8">
            <uq-date v-model="formData.casePunishApproveVO['collectiveDiscussionDate']" label="集体讨论日期" placeholder="请选择" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
          </el-col>
          <el-col :span="8">
            <uq-input v-model="formData.casePunishApproveVO['collectiveDiscussionResult']" label="集体讨论结果" prop="casePunishApproveVO.collectiveDiscussionResult" />
          </el-col>
        </el-row>
      </el-form-item>
    </template>

    <el-form-item label="处罚内容" class="cfnr">
      <el-checkbox-group v-model="formData.casePunishApproveVO['punishmentFlag']" style="width: 200px">
        <el-checkbox :label="item.dmName" :value="item.dmValue" v-for="(item, index) in departContentList" :key="index" />
      </el-checkbox-group>
      <el-form :disabled="!formData.cfFormRight" size="large" label-position="right" label-width="180px" style="padding-left: 200px;">
        <el-row v-if="formData.casePunishApproveVO['punishmentFlag'].includes('1')">
          <el-col :span="8">
            <uq-select label="不予处罚原因" v-model="formData.casePunishApproveVO['noPunishmentReason']" prop="casePunishApproveVO.punishDetail" :options="formData.noPunishmentReasonList" />
          </el-col>
          <el-col :span="8"/>
          <el-col :span="8"/>

          <el-col :span="8" class="mt-20">
            <uq-input v-model="formData.casePunishApproveVO['noPunishmentNum']" label="不予行政处罚决定书文号" prop="casePunishApproveVO.punishmentNum" />
          </el-col>
          <el-col :span="8" class="mt-20">
            <uq-date v-model="formData.casePunishApproveVO['noPunishmentDate']" label="不予行政处罚决定书日期" placeholder="请选择" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
          </el-col>
          <el-col :span="24" class="mt-20">
            <uq-input v-model="formData.casePunishApproveVO['noPunishmentReasonContent']" label="不予行政处罚原因" type="textarea" prop="casePunishApproveVO.correctContent" />
          </el-col>
          <el-form-item  class="uploder mt-10"  label="不予行政处罚决定书审批单">
            <chunk-uploader v-model="formData['bycfspdFileList']" singleFile :fileParams="{instanceId: formData.casePunishApproveVO.id, docType:'bycfspd',docUser:'uqian',docDir:'hlwjjg/punish'}" :disabled="!formData.cfFormRight" />
          </el-form-item>
        </el-row>
      </el-form>
    </el-form-item>

    <template  v-if="!formData.casePunishApproveVO['punishmentFlag'].includes('1')">
      <el-form-item v-for="(item, index) in formData.casePunishApproveDetailVOList" :key="index" >
        <el-checkbox-group v-model="item.checkFlag" style="width: 200px">
          <el-checkbox :label="item.quotaName" value="1" />
        </el-checkbox-group>
        <!-- 子集 -->
        <el-row v-if="item.childList && item.childList.length > 0" class="d-flex" style="width: calc(100% - 200px)">
          <el-col v-for="(childItem, childIndex) in item.childList" :key="childIndex" :span="childItem.quotaCode === 'otherContent' ? 24 : 8">
            <!-- 输入框金额 -->
            <template v-if="childItem.quotaType === 'money'">
              <uq-input v-model="childItem.quotaValue" :label="childItem.quotaName" placeholder="请输入" rules="number">
                <template v-slot:append>
                  <span>元</span>
                </template>
              </uq-input>
            </template>
            <!-- 日期 -->
            <template v-if="childItem.quotaType === 'date'">
              <uq-date v-model="childItem.quotaValue" :label="childItem.quotaName" placeholder="请选择" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
            </template>
            <!-- 输入框天数 -->
            <template v-if="childItem.quotaType === 'days'">
              <uq-input v-model="childItem.quotaValue" :label="childItem.quotaName" placeholder="请输入" rules="number" />
            </template>
            <!-- 输入框内容文本 -->
            <template v-if="childItem.quotaType === 'text'">
              <uq-input v-if="childItem.quotaCode === 'otherContent'" v-model="childItem.quotaValue" :label="''" :placeholder="childItem.quotaName" rules="number" />
              <uq-input v-else v-model="childItem.quotaValue" :label="childItem.quotaName" placeholder="请输入" rules="number" />
            </template>
          </el-col>
        </el-row>
        <el-form-item v-if="item.quotaCode === 'ffcw'" class="uploder mt-10" label-width="380px" label="上传非法所得财务明细文件">
          <chunk-uploader v-model="formData['ffsdcwFileList']" singleFile :fileParams="{instanceId: formData.casePunishApproveVO.id, docType:'ffsdcw',docUser:'uqian',docDir:'hlwjjg/punish'}" :disabled="!formData.formRight" />
        </el-form-item>
      </el-form-item>
      <el-form-item>
        <el-form :disabled="!formData.cfFormRight" size="large" label-position="right" label-width="180px" style="padding-left: 200px;width: 100%;">
          <el-row style="width: 100%;">
            <el-col :span="8">
              <uq-input v-model="formData.casePunishApproveVO['punishmentNum']" label="行政处罚决定书文号" prop="casePunishApproveVO.punishmentNum" />
            </el-col>
            <el-col :span="8">
              <uq-date v-model="formData.casePunishApproveVO['punishmentDate']" label="行政处罚决定书日期" placeholder="请选择" value-format="YYYY-MM-DD" format="YYYY-MM-DD" />
            </el-col>
            <el-col :span="24" v-if="formData.from === 'county'">
              <el-form-item class="uploder mt-20" label="上传行政处罚文书">
                <chunk-uploader v-model="formData['xzcfFileList']" singleFile :fileParams="{instanceId:formData.casePunishApproveVO.id, docType:'xzcf',docUser:'uqian',docDir:'hlwjjg/punish'}" :disabled="!formData.cfFormRight" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form-item>
      <el-form-item  class="uploder mt-10" label-width="560px" label="行政处罚决定书审批单">
        <chunk-uploader v-model="formData['cfspdFileList']" singleFile :fileParams="{instanceId: formData.casePunishApproveVO.id, docType:'cfspd',docUser:'uqian',docDir:'hlwjjg/punish'}" :disabled="!formData.cfFormRight" />
      </el-form-item>
    </template>

  </el-form>
  <uq-dialog v-model:modelValue="showLegalReview" height="60vh" top="10vh" width="1200px" title="法制审核">
    <LegalReview :caseId="formData.casePunishApproveVO.caseId" v-if="showLegalReview" @close="showLegalReview = false" @save="showLegalReview = false; emit('refresh')" />
  </uq-dialog>
  <uq-dialog v-model:modelValue="showSignIn" height="70vh" top="10vh" width="1200px" title="签报">
    <SignIn v-if="showSignIn" @close="showSignIn = false" :caseId="formData.casePunishApproveVO.caseId" @save="showSignIn = false; emit('refresh')" />
  </uq-dialog>
  <uq-dialog v-model:modelValue="showMeeting" height="70vh" top="10vh" width="1200px" title="上会">
    <Meeting v-if="showMeeting" @close="showMeeting = false" :caseId="formData.casePunishApproveVO.caseId" @save="showMeeting = false; emit('refresh')" />
  </uq-dialog>
  <uq-dialog v-model:modelValue="showDocumentSend" height="70vh" top="10vh" width="1200px" title="发文">
    <DocumentSend v-if="showDocumentSend" @close="showDocumentSend = false" :caseId="formData.casePunishApproveVO.caseId" @save="showDocumentSend = false; emit('refresh')" />
  </uq-dialog>
</template>

<style lang="scss" scoped>
  .danger {
    color: #d9001b;
  }
  .uploder {
    width: 100%;
    :deep(.el-form-item__content) {
      height: 100%;
      .uq-uploader-ui {
        height: 100%;
        width: 100%;
      }
    }
  }
  .m-title {
    padding: 5px 10px;
    background-color: #f0f0ff;
    display: flex;
    align-items: center;
  }
  .correctDays {
    margin-top: 3px;
  }
  .d-flex {
    display: flex;
    align-items: center;
  }
  .tips {
    padding: 5px 10px;
    background-color: #fbe2e5;
    font-size: 16px;
    p {
      display: inline-block;
    }
    p.bold {
      font-size: bold;
    }
    p.danger {
      color: #d9001b;
    }
  }
  .cfnr{
    :deep{
      .el-form-item__content{
        align-items: baseline;
      }
    }
  }
</style>
