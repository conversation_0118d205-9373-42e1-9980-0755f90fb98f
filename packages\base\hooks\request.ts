import axios, { AxiosStatic, AxiosRequestConfig, AxiosResponse } from 'axios'
import qs from 'qs'
import { sm3 } from 'sm-crypto'
import onpenHtml from './open'
declare module 'axios' {
  export interface AxiosRequestConfig {
    [key: string]: any
  }
  interface AxiosInstance {
    (_config: AxiosRequestConfig): Promise<any>
    [key: string]: any
  }
}
/**
1、统一认证登录跳转index.html，地址栏传参 view、token、cityFlag、authorizeUrl，前端缓存token、cityFlag、authorizeUrl
2、正常退回登录传给后端isLocalLogin、cityFlag
3、token失效前端根据isLocalLogin跳转本地登录页或者重定向到authorizeUrl
 */
const goLogin = (url?: string | undefined) => {
  const baseLoginUrl = 'http://172.26.56.5/cas/qxLogin?appResId=BJFGW_CLOUD_HLWJJG&service=http://172.26.76.216/gateway/auth/login/cas'
  const isLogin = window.sessionStorage.getItem('token')
  // 如果是从本系统的登录页面登录的的话，就返回到本地的登录页面。
  if (isLogin === 'true') {
    onpenHtml({ url: (url || window.sessionStorage.getItem('loginUrl') || baseLoginUrl) })
  } else {
    const authorUrl: any = window.sessionStorage.getItem('authorizeUrl')
    if (authorUrl) {
      onpenHtml({ url: (url || authorUrl || baseLoginUrl ) })
    } else {
      onpenHtml({ url: (url || window.sessionStorage.getItem('loginUrl') || baseLoginUrl) })
    }
  }
  window.sessionStorage.clear()
}
// 导入element ui的loading 和错误消息弹框
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
const requestFun = axios.create({
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json;charset=UTF-8;text/plain'
  }
})
// axios 配置
let loadingInstance: { close: (_param?: boolean) => void } | null = null
//请求对象config需要进行扩展
interface MYRequestConfig extends AxiosRequestConfig {
  needMsg?: boolean//是否需要返回值的message
  catch?: boolean //是否需要自定义处理catch
  custom?: string | undefined // part 自定义局部loading
  headers: any // 请求头
  type?: string // 请求类型 query / body
  [key: string]: any
}
const options = {
  background: 'rgba(0, 0, 0, 0)',
  fullscreen: true
}
// http request 请求 拦截器
// 生成环境
requestFun.interceptors.request.use(
  (config: MYRequestConfig): any => {
    if (config['custom'] !== 'part') {
      // target用了这个属性以后每次生成都会生成一个新的实例要先检测有没有原来了有就关闭不然页面上一直loading
      loadingInstance = ElLoading.service(options)
    }
    // 请求头文件流下载 如果是下载则需要在api文件里面加添加responseType = blob 此处的结果应为 config.responseType: blob
    // 如果data是一个字符串 那就要设置'Content-Type': 'text/plain'
    // 防止传给后端的参数多出双引号或者等号
    if (typeof config.data === 'string') {
      config.headers['Content-Type'] = 'text/plain'
    }
    // 获取token
    const resultToken = window.sessionStorage.getItem('token')
    if (resultToken) {
      // 判断本地token是否存在
      config.headers.Authorization = resultToken // 将token设置成请求头
    }
    if (config.method === 'get') {
      if (config.data) config.url = config.url + '?' + qs.stringify(config.data)
    }
    // POST 提交统一使用form-urlencoded提交 (比如 id=1&type=2)
    if (config.type === 'query') {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      if (typeof config.data !== 'string') {
        config.data = qs.stringify(config.data, { arrayFormat: 'repeat' })
      }
    } else {
      if (config.data) {
        config.headers['smJson'] = sm3(typeof config.data === 'object' ? JSON.stringify(config.data) : config.data)
      }
    }
    return config
  },
  (err) => {
    loadingInstance?.close()
    return Promise.reject(err)
  }
)
// http response 响应拦截器
requestFun.interceptors.response.use(
  (response: AxiosResponse<any, any>): any => {
    try {
      return new Promise((resolve, reject) => {
        res(response, resolve, reject)
      })
    } catch (e) {
      loadingInstance?.close()
      console.log(e)
    }
  },
  async (error) => {
    loadingInstance?.close()
    try {
      return await Promise.reject(error)
    } catch (e: any) {
      const codeMsg = e.response ? e.response.data : '无效的访问请求，请检查地址是否正确。'
      const str = typeof codeMsg === 'string' ? codeMsg : codeMsg.message
      const notify401Dom = document.querySelector('.el-message-box.notify-401.el-message-box--center')
      if (!notify401Dom) {
        if (codeMsg?.code === '401') {
          const boxConfig: any = {
            title: '温馨提示',
            message: `<div style="text-align: left; max-height: 300px" class="over-auto">${str}</div>`,
            type: 'warning',
            customClass: 'notify-401',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '去登录',
            closeOnClickModal: false,
            showClose: false,
            center: true
          }
          ElMessageBox({ ...boxConfig }).then(() => {
            goLogin()
          })
        } else {
          ElMessageBox({
            title: '操作异常，请联系管理员。',
            message: `<div style="text-align: left; max-height: 300px" class="over-auto">${str}</div>`,
            type: 'error',
            customClass: 'notify-401',
            dangerouslyUseHTMLString: true,
            confirmButtonText: '好的',
            showClose: false,
            center: true
          })
            .then((e: any) => e)
            .catch((e: any) => e)
        }
      }
      return await Promise.reject(error)
    }
  }
)

function res(
  response: AxiosResponse<any, any>,
  resolve: { (value: AxiosResponse<any, any> | PromiseLike<AxiosResponse<any, any>>): void; (arg0: AxiosResponse<any, any>): void },
  reject: { (_reason?: any): void; (_arg0: unknown): void }
) {
  try {
    if (response) {
      if (response.status === 200) {
        loadingInstance?.close()
        if (response.headers['new_uqian_token']) {
          // 替换本地token
          window.sessionStorage.setItem('token', response.headers['new_uqian_token'])
        }
        if (response.headers['content-disposition'] && response.headers['content-disposition'].includes('attachment')) {
          // 文件下载
          resolve(response)
        } else {
          const config: MYRequestConfig = response.config
          const data: any = response.data
          if (config && config.needMsg) {
            // 需要个性化 判断 code
            resolve(data)
          } else {
            const resCode = Number(data.code)
            if (resCode === 200) {
              // 正常返回请求成功
              resolve(data.data)
            } else if (resCode === 445) {
              alert445(data.message, () => {
                ElMessageBox.close()
              })
            } else if (resCode === 401) {
              // 单点登录重新回到登录页面
              goLogin(data?.data?.toUrl)
            } else {
              if (response['request']['responseType'] === 'blob') {
                if (response['headers']['content-type'].includes('application/json')) {
                  const reader = new FileReader()
                  reader.readAsText(data)
                  reader.onload = () => {
                    const { result } = reader
                    const a = JSON.parse(result as string)
                    ElMessage.error(a['message'])
                  }
                } else {
                  // 应对直接返回文件流的情况
                  resolve(response)
                }
              } else {
                // ElMessage.error(data.message)
                ElMessage({
                  message: (typeof data.message === 'string'
                    ? data.message.replace(/(\r\n|\n|\r)/g, '<br/>')
                    : data.message),
                  type: 'error',
                  dangerouslyUseHTMLString: true
                })

                // catch === true 自己捕获catch数据，并做出回调 否则，用公共的方法捕获错误
                if (config && config.catch) {
                  reject(data)
                } else {
                  Promise.reject(data).catch((e) => {
                    reject(e)
                  })
                }
              }
            }
          }
        }
      } else if (response.status === 206) {
        loadingInstance?.close()
        resolve(response)
      } else {
        // 这里和后端开发人员约定 错误返回值
        ElMessage.error('请求失败')
      }
    } else {
      loadingInstance?.close()
      ElMessage.error((response as any).statusText)
    }
  } catch (e) {
    loadingInstance?.close()
    ElMessage.error(reject(e) as any)
  }
  loadingInstance?.close()
  // loading关闭
}
let alert445Timer: any
function alert445(msg: any, closeFun: { (): void; (): void }) {
  // 445 操作过于频繁
  if (alert445Timer) {
    return
  }
  let second = 10
  alert445Timer = setInterval(() => {
    second--
    const secondDom = document.getElementById('wait-second') as HTMLDivElement
    secondDom.innerHTML = second + ''
    if (second === 0) {
      closeFun()
      clearInterval(alert445Timer!)
      alert445Timer = undefined
    }
  }, 1000)
  ElMessageBox({
    title: '休息一下，喝杯茶吧 ！',
    message: `<p>${msg}在 <span id="wait-second" style="color: orange;">${second}</span> 秒后请重试。</p>`,
    type: 'warning',
    iconClass: 'el-icon-hot-water color-orange',
    dangerouslyUseHTMLString: true,
    showConfirmButton: false,
    closeOnPressEscape: false,
    closeOnClickModal: false,
    showClose: false,
    center: true
  } as any).then((res: any) => res)
}

export default requestFun as AxiosStatic
