server {
  listen 9503;
  client_max_body_size 1024m;
  # 关键配置 start
  proxy_set_header  Host $host;
  proxy_set_header  X-Real-IP  $remote_addr;
  proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header  X-Forwarded-Proto   $scheme;
  proxy_set_header Cookie $http_cookie;
  proxy_request_buffering off;
  proxy_http_version 1.1;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "upgrade";
  # 关键配置 end

  location /login/ {
		proxy_pass   http://127.0.0.1:9534/login/;
    proxy_cookie_path /login /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	location /console/ {
		proxy_pass   http://127.0.0.1:9535/console/;
    proxy_cookie_path /console /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	location /form/ {
		proxy_pass   http://127.0.0.1:9536/form/;
		proxy_cookie_path /form /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	location /flowable-designer/ {
		proxy_pass   http://127.0.0.1:9537/flowable-designer/;
		proxy_cookie_path /flowable-designer /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	location /flowable-viewer/ {
		proxy_pass   http://127.0.0.1:9538/flowable-viewer/;
		proxy_cookie_path /flowable-viewer /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
  location /default/ {
		proxy_pass   http://127.0.0.1:9539/default/;
		proxy_cookie_path /default /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
  location /project/ {
		proxy_pass   http://127.0.0.1:9540/project/;
		proxy_cookie_path /project /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	location /gateway/ {
		proxy_pass   http://192.168.1.144:8500/gateway/;
		proxy_cookie_path /gateway /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
	## swagger
	location /v3/api-docs/ {
		proxy_pass   http://192.168.1.144:8500/v3/api-docs/;
		proxy_cookie_path /gateway /;
	}
	location /webjars/ {
		proxy_pass   http://192.168.1.144:8500/webjars/;
		proxy_cookie_path /gateway /;
	}
	location /doc.html {
		proxy_pass   http://192.168.1.144:8500;
		proxy_cookie_path /gateway /;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
  location /pdfjs-3.0.279-dist/ {
    alias html/pdfjs-3.0.279-dist/;
    ## html不缓存
    if ($request_filename ~* .*\.(htm|html)$){
      add_header Cache-Control "no-store";
    }
	}
  # 高远 139
  # 张绍辉 221
  # 刘新艳 144
  # 杨倩倩 119
  # 张春喜 145
}
