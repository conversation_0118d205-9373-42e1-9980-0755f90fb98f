<script setup lang='ts'>
defineOptions({ name: 'TopComp' })
import { uqConfirm, openHtml } from '@uq/base/hooks'
import { logout } from '@/api/home.ts'

import { useRouter } from 'vue-router'
const router = useRouter()

import { projectStore } from '../../store'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { topCurrentMenu, pageJump } = storeToRefs(store)

const props = withDefaults(
  defineProps<{
    menuList: any[]
    userInfo: any
  }>(), {}
)

const emit = defineEmits(['update:leftMenuList'])

const currentMenu = ref()
currentMenu.value = props.menuList[0]

watch(() => topCurrentMenu.value, (val: any) => {
  if (pageJump.value){
    menuClick(val)
    store.setPageJump(true)
  } else {
    store.setPageJump(false)
  }
}, { deep: true }
)

if (topCurrentMenu.value.menuUrl === '/HomePage'){
  emit('update:leftMenuList', '/HomePage')
  router.push(topCurrentMenu.value.menuUrl )
} else {
  emit('update:leftMenuList', topCurrentMenu.value?.children || [])
}
const unReadCount = ref(0)
const menuClick = (item: any) => {
  // 先初始化左侧菜单
  if (item.menuUrl === '/HomePage') {
    emit('update:leftMenuList', '/HomePage')
    router.push(item.menuUrl)
  } else {
    // 这里加一个深拷贝，确保 emit 的值每次都不同，触发响应
    emit('update:leftMenuList', JSON.parse(JSON.stringify(item?.children || [])))
  }
  // 只要点击就更新
  topCurrentMenu.value = item
  store.setTopCurrentMenu(item)
  store.setPageJump(false)
  window.sessionStorage.removeItem('searchData')
}
// const getUnReadCount = () => {
//   countPageMessage({
//     isPage: true,
//     queryParam: {
//       isRead: 0
//     }
//   }).then(res => {
//     unReadCount.value = res.total
//   })
// }
// 退出登录
const logoutFun = () => {
  uqConfirm('确定退出吗？', () => {
    logout({ isLocalLogin: window.sessionStorage.getItem('login'), cityFlag: window.sessionStorage.getItem('cityFlag') }).then((r: any) => {
      const loginUrl: string | null = r?.toUrl === 'login' ? '/login' : r?.toUrl
      openHtml({ url: loginUrl })
      window.sessionStorage.clear()
    })
  })
}
</script>

<template>
  <div class="top-comp">
    <img class="logo" src="../../assets/images/logo.png" alt="">
    <div class="top-menu">
      <div :class="{active: topCurrentMenu.menuId === item.menuId}" @click="menuClick(item)" class="top-meun-item" v-for="item in menuList" :key="item.menuId">{{ item.menuName }}</div>
    </div>
    <el-popover popper-class="message-popper" placement="bottom" width="350" trigger="click">
      <MessageList @getUnReadCount="(count: number) => unReadCount = count" />
      <template v-slot:reference>
        <div  class="fc flr msg-pop">
          <i style="color: #fff;" class="uq-iconfont icon-uq_notify" />
          <div v-if="unReadCount>0" class="d">●</div>
          <!-- <div v-if="unReadCount>0" class="un-read">{{ unReadCount }}</div> -->
        </div>
      </template>
    </el-popover>
    <div class="user">
      <p>欢迎您：{{ userInfo.userName }}</p>
      <img @click="logoutFun" src="../../assets/images/logout.png" alt="">
    </div>
  </div>
</template>

<style lang='scss' scoped>
.fc {
  justify-content: center;
  align-items: center;
  justify-items: center;
  align-content: center;
}
.flr {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}
.msg-pop {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    height: 100%;
    position: relative;
    i {
      font-size: 20px;
    }
  }
  .un-read {
    position: absolute;
    padding: 0 10px;
    border-radius: 10px;
    background: #f5222d;
    height: 18px;
    line-height: 18px;
    top: 8px;
    transform: translateX(50%);
  }
  > div {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    &.logout:hover {
      background-color: rgb(215, 43, 3);
      color: #ffffff;
    }
    > i {
      font-size: 20px;
    }
  }
.top-comp{
  padding: 25px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .logo{
    width: 648px;
  }
  .top-menu{
    padding-left: 100px;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;
    .top-meun-item{
      color: #fff;
      font-size: 20px;
      cursor: pointer;
      &::after{
        content: '';
        display: inline-block;
        height: 5px;
        width: 100%;
        border-radius: 5px;
      }
      &.active::after{
        background-color: #fff;
      }
    }
  }
  .user{
    display: flex;
    align-items: center;
    gap: 11px;
    p{
      font-size: 16px;
      color: #fff;
    }
    img{
      width: 41px;
      height: 41px;
      cursor: pointer;
    }
  }
}
.d{
  color: #ee0a24;
  font-size: 10px;
  margin-top: -15px;
}
</style>
