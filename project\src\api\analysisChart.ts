import request from '@uq/base/hooks/request'
const baseUrl = import.meta.env.VITE_BASE_URL
const contextPath = baseUrl
// 获取字典接口
export function getCodeCommons(data: object) {
  return request({
    baseURL: baseUrl,
    url: `/base/dictionary/code/getCodeCommons`,
    type: 'query',
    method: 'post',
    data
  })
}

// 行政复议分析-按涉案领域分析
export function pieCaseTypeReconsider(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/reconsider/analyse/pieCaseTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政复议分析-按审理结果分析
export function pieDecideTypeReconsider(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/reconsider/analyse/pieDecideTypeAnalyse`,
    method: 'post',
    data
  })
}// 行政复议分析-涉案处室分析
export function listOfficeAnalyse(data: any) {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/reconsider/analyse/listOfficeAnalyse`,
    method: 'post',
    data
  })
}
// 行政复议分析-涉案领域同比分析
export function yoyCaseTypeReconsider(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/reconsider/analyse/yoyCaseTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政复议分析-审理结果同比分析
export function yoyDecideTypeReconsider(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/reconsider/analyse/yoyDecideTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政诉讼分析-按涉案领域分析
export function pieCaseTypeLitigate(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/litigate/analyse/pieCaseTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政诉讼分析-按审理结果分析
export function pieDecideTypeLitigate(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/litigate/analyse/pieDecideTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政诉讼分析-涉案领域同比分析
export function yoyCaseTypeLitigate(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/litigate/analyse/yoyLitigateTypeAnalyse`,
    method: 'post',
    data
  })
}
// 行政诉讼分析-审理结果同比分析
export function yoyDecideTypeLitigate(data: { data: string }): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/litigate/analyse/yoyDecideTypeAnalyse`,
    method: 'post',
    data
  })
}

// 行政执法检查信息公示-统计分析-列表初始化
export function initPageAnnouncement(): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/announcement/olap/initPageAnnouncement`,
    method: 'post',
  })
}

// 行政执法检查信息公示-统计分析-列表
export function listAnnouncement(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/announcement/olap/listAnnouncement`,
    method: 'post',
    data
  })
}

// 行政执法检查信息公示-统计分析-柱图
export function olapAnnouncementBar(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/announcement/olap/olapAnnouncementBar`,
    method: 'post',
    data
  })
}

// 行政执法检查信息公示-统计分析-饼图
export function olapAnnouncementPe(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/announcement/olap/olapAnnouncementPe`,
    method: 'post',
    data
  })
}

// 风险预警统计分析-数值统计
export function getRiskWarnStatisticsInfo(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnStatisticsInfo`,
    method: 'post',
    data
  })
}

// 风险预警统计分析-按监管领域分析
export function getRiskWarnDomainAnalysis(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnDomainAnalysis`,
    method: 'post',
    data
  })
}

// 风险预警统计分析-风险等级占比分析
export function getRiskWarnGradePieInfo(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnGradePieInfo`,
    method: 'post',
    data
  })
}

// 风险预警统计分析-预警结果处置情况分析
export function getRiskWarnResultQueryCount(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnResultQueryCount`,
    method: 'post',
    data
  })
}

// 风险预警统计分析-各领域待处置预警情况分析
export function getRiskWarnSituation(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnSituation`,
    method: 'post',
    data
  })
}
// 风险预警统计分析-各风险等级分监管领域预警量占比分析
export function getRiskWarnCheckGradePieInfo(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/risk/statistics/getRiskWarnCheckGradePieInfo`,
    method: 'post',
    data
  })
}

// 卷宗统计分析-案卷归档列表条件初始化
export function initQueryParam(): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/fileArchive/initQueryParam`,
    method: 'post',
  })
}
// 卷宗统计分析-案件来源占比分析
export function statisticsChartsByCaseSource(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsChartsByCaseSource`,
    method: 'post',
    data
  })
}
// 卷宗统计分析-卷宗完整性分析
export function statisticsChartsByFull(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsChartsByFull`,
    method: 'post',
    data
  })
}
// 卷宗统计分析-执法领域卷宗数量分布分析
export function statisticsChartsByCheckType(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsChartsByCheckType`,
    method: 'post',
    data
  })
}
// 卷宗统计分析-各案件处理分类分监管领域预警量占比分析
export function statisticsChartsByHandleClassAndCheckType(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsChartsByHandleClassAndCheckType`,
    method: 'post',
    data
  })
}
// 卷宗统计分析-卷宗数量分布分析(按企业所在区)
export function statisticsChartsByDistrict(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsChartsByDistrict`,
    method: 'post',
    data
  })
}
// 卷宗统计分析-按办理类型统计个数
export function statisticsCaseHandleCount(data: any): any {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/assist/archive/analysis/statisticsCaseHandleCount`,
    method: 'post',
    data
  })
}

// 统计数
export function statisticLearn() {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/exam/learnStatistic/statisticLearn`,
    method: 'post'
  })
}

// 预考计划管理-统计数
export function planPerson(data: any) {
  return request({
    baseURL: contextPath,
    url: `/hlwjjg/exam/plan/planPerson`,
    method: 'post',
    data
  })
}
