<script setup lang="ts">
defineOptions({ name: 'CaseInformation' })
import { listIllegalGistByPowerId } from '../../../api/administrativePenalty'
import { FormInstance, FormRules } from 'element-plus'
import { ref, watch } from 'vue'

const props = defineProps({
  initData: {
    type: Object,
    default: () => ({
      casePartyLegal: {}
    })
  }
})

watch(
  () => props.initData,
  (val: any) => {
    formData.value = val
  },
  { deep: true }
)
// 修改 formData 的类型定义
type SimpleObject = Record<string, any>
const formData = ref<SimpleObject>(props.initData)

// 优化 checkNum 相关逻辑
const selectedCheckNum = JSON.parse(JSON.stringify(formData.value.caseBasic?.checkNum))
const isCheckNumDisabled = computed(() => {
  const caseWay = formData.value.caseBasic?.caseWay
  const hasCheckNum = !!selectedCheckNum
  const hasDocument = formData.value.caseDocumentMap?.document?.length > 0
  if (formData.value.source === 'district') {
    return caseWay === '1' || (caseWay === '2' && hasCheckNum)
  }
  return caseWay === '1' || (caseWay === '2' && hasDocument && hasCheckNum)
})

// 选择执法职权下拉框选择后，联动执法依据下拉框数据更新
const violationLaw = (value: any, index: any) => {
  const item = formData.value.powerCodeList.find((e: any) => e.powerId === value)
  // 禁用已选择的执法职权下拉框选项
  formData.value.powerCodeList.map((e: any) => {
    const selected = formData.value['casePowerList'].find((v: any) => v.powerId === e.powerId)
    e.disabled = !!selected
  })
  // 过滤掉已经被选择的执法依据下拉框数据，避免重复选择
  if (formData.value['illegalGistList'] && formData.value['illegalGistList'].length > 0) {
    const powerIds = formData.value['casePowerList'].map((e: any) => e.powerId)
    const illegalGistList = JSON.parse(JSON.stringify(formData.value['illegalGistList']))
    formData.value['illegalGistList'] = illegalGistList.filter((e: any) => powerIds.includes(e.powerId))
  }
  // 只更新当前行的 powerId 和 powerName
  if (item) {
    formData.value['casePowerList'][index].powerId = item.powerId
    formData.value['casePowerList'][index].powerName = item.powerName
    formData.value['casePowerList'][index].powerCode = item.powerCode
    listIllegalGistByPowerId({ data: value }).then((res: any) => {
      if (formData.value.illegalGistList && formData.value.illegalGistList.length > 0) {
        formData.value.illegalGistList = formData.value.illegalGistList.concat(res)
      } else {
        formData.value.illegalGistList = res
      }
    })
  }
}
const deleteViolation = (val: any, index: any) => {
  // 根据代码上下文，推测这里要操作的是 casePowerList 数组
  const list = JSON.parse(JSON.stringify(formData.value['illegalGistList'] || []))
  formData.value['illegalGistList'] = list.filter((item: any) => item.powerId !== val.powerId)
  formData.value.casePowerList.splice(index, 1)
  formData.value.powerCodeList.map((e: any) => {
    if (e.powerId === val.powerId) {
      e.disabled = false
    }
  })
}
const saveViolation = () => {
  if (!formData.value['casePowerList']) formData.value['casePowerList'] = []
  formData.value['casePowerList'].push({ powerId: '', powerCode: '' })
}
const formRef = ref<FormInstance>()
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch {
    return false
  }
}
const checkNumClick = (val: any) => {
  if (val) {
    const item = formData.value.checkNumList.find((e:any) => e.checkNum === val)
    if (item) {
      formData.value.caseBasic.checkType = item.checkType

      const userItem = formData.value.userList.find((e:any) => e.dmValue === item.memberId)
      // if (!formData.value.caseBasic.memberId){
      if (userItem) {
        formData.value.caseBasic.memberName = userItem.dmName
        formData.value.caseBasic.memberId = userItem.dmValue
        // }
      }

      formData.value.caseBasic.checkDate = item.checkDate
      formData.value.casePartyLegal.partyType = item.partyType
      formData.value.casePartyLegal.enterpriseName = item.enterpriseName
      formData.value.casePartyLegal.enterpriseType = item.enterpriseType
      formData.value.casePartyLegal.uniformSocialCreditCode = item.uniformSocialCreditCode
      formData.value.casePartyLegal.legalPerson = item.legalPerson
      formData.value.casePartyLegal.legalPersonPhone = item.legalPersonPhone
      formData.value.casePartyLegal.delegator = item.delegator
      formData.value.casePartyLegal.delegatorPhone = item.delegatorPhone
      formData.value.casePartyLegal.enterpriseAddress = item.enterpriseAddress
    }
  } else {
    formData.value.caseBasic.checkType = ''
  }
}
const memberIdClick = (val: any) => {
  if (val) {
    const item = formData.value.userList.find((e:any) => e.dmValue === val)
    if (item) {
      formData.value.caseBasic.memberName = item.dmName
    }
  } else {
    formData.value.caseBasic.memberName = ''
  }
}

const rules = reactive<FormRules>({
  'casePartyLegal.enterpriseType': [
    { required: true, message: '请选择企业类型', trigger: 'change' }
  ],
})

// 暴露数据获取方法
const getFormData = () => ({ ...formData })
// 暴露给父组件的方法清单
defineExpose({
  validateForm,
  getFormData
})
</script>
<!-- PunishCity/AdministrativePenalty -->
<!-- 案件基本信息 -->
<template>
  <div class="case-information">
    <el-form class="uq-form" ref="formRef" :model="formData" :disabled="!formData.formRight" label-position="right" label-width="150px" :rules="rules">
      <div class="model-title"> 基本信息 </div>
      <div class="information-item">
        <uq-input v-model="formData.caseBasic['caseName']" label="案件名称"  placeholder="请输入案件名称" prop="caseBasic.caseName" rules="required" />
        <el-row>
          <el-col :span="12">
            <uq-input v-model="formData.caseBasic['caseNum']" label="立案编号" :disabled="true"  placeholder="请输入立案编号" />
          </el-col>
          <el-col :span="12">
            <uq-input v-model="formData.caseBasic['deptName']" label="执法主体" :disabled="true"  placeholder="请输入执法主体" />
          </el-col>
          <el-col :span="12">
            <uq-select v-model="formData.caseBasic['caseSource']" label="案件来源"  :options="formData.caseSourceList" placeholder="请选择案件来源" prop="caseBasic.caseSource" rules="required" />
          </el-col>
          <el-col :span="12">
            <uq-input v-model="formData.caseBasic['officeName']" label="执法处室" :disabled="formData.source === 'district' ? false : true"  placeholder="请输入执法处室" />
          </el-col>
          <template v-if="formData.caseBasic['caseSource']==='1'">
            <el-col :span="12">
              <!-- <template  v-if="(formData.checkNumList&&formData.checkNumList.length!==0)">
                <uq-select v-if="formData.source === 'district'" v-model="formData.caseBasic['checkNum']"  label="检查单号" :disabled="formData.caseBasic['caseWay']==='1' || (formData.caseBasic['caseWay']==='2' && !!checkNum)" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum" rules="required" @change="checkNumClick" />
                <uq-select v-else v-model="formData.caseBasic['checkNum']"  label="检查单号" :disabled="formData.caseBasic['caseWay']==='1' || (formData.caseBasic['caseWay']==='2' && formData.caseDocumentMap?.document?.length > 0 && !!checkNum)" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum" rules="required" @change="checkNumClick" />
              </template> -->
              <uq-select  v-if="(formData.checkNumList&&formData.checkNumList.length!==0)" v-model="formData.caseBasic['checkNum']"  label="检查单号" :disabled="isCheckNumDisabled" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum" rules="required" @change="checkNumClick" />

              <el-form :disabled="true" v-else label-width="150px">
                <uq-input v-model="formData.caseBasic['checkNum']" label="检查单号"  placeholder="请输入检查单号" :disabled="true" />
              </el-form>
            </el-col>
            <el-col :span="12">
              <uq-date v-model="formData.caseBasic['checkDate']" label="检查日期"  placeholder="请选择检查日期" prop="caseBasic.checkDate" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-select v-model="formData.caseBasic['caseCheckType']" label="检查类型"  :options="formData.inspectionTypeList" placeholder="请选择检查类型" prop="caseBasic.caseCheckType" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-select v-model="formData.caseBasic['checkWay']" label="检查方式"  :options="formData.checkWayList" placeholder="请选择检查方式" prop="caseBasic.checkWay" rules="required" />
            </el-col>
            <el-col :span="24">
              <uq-input v-model="formData.caseBasic['casePlace']" label="案件发生地"  placeholder="请输入案件发生地" prop="caseBasic.casePlace" rules="required" />
            </el-col>
            <el-col :span="24">
              <uq-input v-model="formData.caseBasic['basicInfo']" label="案件基本情况" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入案件基本情况" prop="caseBasic.basicInfo" rules="required" />
            </el-col>
          </template>
          <template v-else-if="formData.caseBasic['caseSource']==='2'">
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complaintSource']" label="投诉来源"  placeholder="请输入投诉来源" />
            </el-col>
            <el-col :span="12">
              <uq-select v-if="formData.checkNumList&&formData.checkNumList.length!==0" v-model="formData.caseBasic['checkNum']" label="检查单号" :disabled="isCheckNumDisabled" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum"   @change="checkNumClick" />
              <uq-input v-else v-model="formData.caseBasic['checkNum']" label="检查单号"  placeholder="请输入检查单号" :disabled="true" />
            </el-col>
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['workNum']" label="工单号"  placeholder="请输入工单号" prop="caseBasic.workNum" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-date v-model="formData.caseBasic['otherDate']" label="投诉日期"  placeholder="请选择投诉日期" prop="caseBasic.otherDate" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complainant']" label="投诉人"  placeholder="请输入投诉人" />
            </el-col>

            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complaintPhone']" label="投诉人联系方式"  placeholder="请输入投诉人联系方式" prop="caseBasic.complaintPhone" rules="isPhoneMobileZhbg" />
            </el-col>
            <el-col :span="24">
              <uq-input v-model="formData.caseBasic['basicInfo']" label="投诉事项" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入投诉事项" prop="caseBasic.basicInfo" rules="required" />
            </el-col>
          </template>
          <template v-else-if="formData.caseBasic['caseSource']==='3'">
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complaintSource']" label="举报来源"  placeholder="请输入举报来源" />
            </el-col>
            <el-col :span="12">
              <uq-select v-if="formData.checkNumList&&formData.checkNumList.length!==0" v-model="formData.caseBasic['checkNum']" label="检查单号" :disabled="isCheckNumDisabled" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum"  @change="checkNumClick"  />
              <uq-input v-else v-model="formData.caseBasic['checkNum']" label="检查单号"  placeholder="请输入检查单号" :disabled="true" />
            </el-col>
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['workNum']" label="工单号"  placeholder="请输入工单号" prop="caseBasic.workNum" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-date v-model="formData.caseBasic['otherDate']" label="举报日期"  placeholder="请选择举报日期" prop="caseBasic.otherDate" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complainant']" label="举报人"  placeholder="请输入举报人" />
            </el-col>
            <el-col :span="12">
              <uq-input v-model="formData.caseBasic['complaintPhone']" label="举报人联系方式"  placeholder="请输入举报人联系方式" prop="caseBasic.complaintPhone" rules="isPhoneMobileZhbg" />
            </el-col>
            <el-col :span="24">
              <uq-input v-model="formData.caseBasic['basicInfo']" label="举报事项" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入举报事项" prop="caseBasic.basicInfo" rules="required" />
            </el-col>
          </template>
          <template v-else-if="formData.caseBasic['caseSource']==='7'">
            <el-col :span="12">
              <uq-date v-model="formData.caseBasic['otherDate']" label="其他日期"  placeholder="请选择其他日期" prop="caseBasic.otherDate" rules="required" />
            </el-col>
            <el-col :span="12">
              <uq-select v-if="formData.checkNumList&&formData.checkNumList.length!==0" v-model="formData.caseBasic['checkNum']" label="检查单号" :disabled="isCheckNumDisabled" :options="formData.checkNumList" placeholder="请选择检查单号" prop="caseBasic.checkNum" itemLabel="checkNum" itemValue="checkNum" @change="checkNumClick"  />
              <uq-input v-else v-model="formData.caseBasic['checkNum']" label="检查单号"  placeholder="请输入检查单号" :disabled="true" />
            </el-col>
            <el-col :span="24">
              <uq-input v-model="formData.caseBasic['basicInfo']" label="其他事项" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入其他事项" prop="caseBasic.basicInfo" rules="required" />
            </el-col>
          </template>

          <el-col :span="12">
            <uq-date v-model="formData.caseBasic['filingDate']" label="申请立案日期"  placeholder="请选择申请立案日期" prop="caseBasic.filingDate" rules="required" />
          </el-col>
          <el-col :span="12">
            <uq-input v-model="formData.caseBasic['leaderName']" :disabled="true" label="主办人员"  placeholder="请输入执法人员" />
          </el-col>
          <el-col :span="12">
            <uq-select v-if="formData.userList&&formData.userList.length!==0" v-model="formData.caseBasic['memberId']" label="协办人员"  :options="formData.userList" placeholder="请选择协办人员" prop="caseBasic.memberId" rules="required" @change="memberIdClick" />
            <uq-input v-else v-model="formData.caseBasic['memberName']" :disabled="true" label="协办人员"  placeholder="请输入协办人员"/>
          </el-col>
          <el-col :span="12">
            <uq-date v-model="formData.caseBasic['registerDate']" label="登记日期"  placeholder="请选择登记日期" prop="caseBasic.registerDate" rules="required" />
          </el-col>
        </el-row>
      </div>
      <div class="model-title"> 当事人信息 </div>
      <el-form-item label="当事人类型">
        <el-radio-group v-model="formData.casePartyLegal['partyType']" :disabled="true">
          <el-radio v-for="(item,index) in formData.partyTypeList" :key="index" :value="item.dmValue">{{ item.dmName }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['enterpriseName']" label="单位名称"  placeholder="请输入单位名称" prop="casePartyLegal.enterpriseName" rules="required" />
        </el-col>
        <el-col :span="12">
          <el-form-item class="uploder" label="企业类型" required prop="casePartyLegal.enterpriseType">
            <el-cascader v-model="formData.casePartyLegal['enterpriseType']" :props="{value:'dmValue',label:'dmName',children:'childrenList',emitPath:false}" placeholder="请选择企业类型" :options="formData.enterpriseTypeTree" size="default" style="width: 100%;" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['uniformSocialCreditCode']" label="统一社会信用代码"  placeholder="请输入统一社会信用代码" prop="casePartyLegal.uniformSocialCreditCode" rules="required|isUniformSocialCreditCode" />
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['legalPerson']" label="法定代表人"  placeholder="请输入法定代表人" prop="casePartyLegal.legalPerson" rules="required" />
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['legalPersonPhone']" label="法定代表人联系方式"  placeholder="请输入法定代表人联系方式" prop="casePartyLegal.legalPersonPhone" rules="required|isPhoneMobileZhbg" />
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['delegator']" label="授权委托人"  placeholder="请输入授权委托人" />
        </el-col>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['delegatorPhone']" label="授权委托人联系方式"  placeholder="请输入授权委托人联系方式" prop="casePartyLegal.delegatorPhone" rules="isPhoneMobileZhbg" />
        </el-col>
        <el-col :span="12"/>
        <el-col :span="12">
          <uq-input v-model="formData.casePartyLegal['enterpriseAddress']" label="单位地址"  placeholder="请输入单位地址" prop="casePartyLegal.enterpriseAddress" rules="required" />
        </el-col>
        <el-col :span="12">
          <uq-select v-model="formData.casePartyLegal['district']" label="企业所在地" :options="formData.districtList" placeholder="请选择企业所在地" prop="casePartyLegal.district" rules="required" />
        </el-col>
        <el-col :span="24">
          <el-form-item class="uploder" label="营业执照（复印件）" rule="required">
            <chunk-uploader v-model="formData.casePartyLegal['businessLicenseList']" :fileParams="{instanceId:formData.casePartyLegal.id,docType:'businessLicense',docUser:'uqian',docDir:'hlwjjg/punish/caseParty'}" :disabled="!formData.formRight" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item class="uploder" label="授权委托书" rule="required">
            <chunk-uploader v-model="formData.casePartyLegal['authorizationList']" :fileParams="{instanceId:formData.casePartyLegal.id,docType:'authorization',docUser:'uqian',docDir:'hlwjjg/punish/caseParty'}"  :disabled="!formData.formRight" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item class="uploder" label="受委托人身份证复印件" rule="required">
            <chunk-uploader v-model="formData.casePartyLegal['idCardList']" :fileParams="{instanceId:formData.casePartyLegal.id,docType:'casePartyLegal',docUser:'uqian',docDir:'hlwjjg/punish/caseParty'}" :disabled="!formData.formRight" />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="model-title"> 执法职权信息 </div>
      <el-table :data="formData['casePowerList']" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="date" label="违法行为" align="center">
          <template #default="scope">
            <el-select v-model="scope.row.powerId" placeholder="请选择违法行为" filterable @change="(val) => violationLaw(val, scope.$index)" size="default">
              <el-option v-for="item in formData.powerCodeList" :key="item.powerId" :label="item.powerName" :value="item.powerId" :disabled="item.disabled" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="powerCode" label="职权编号" align="center" width="180" />
        <el-table-column prop="address" label="操作" align="center" width="180">
          <template #default="scope">
            <el-button type="danger" link size="large" circle @click="deleteViolation(scope.row,scope.$index)">删除</el-button>
          </template>
          <template #header>
            <el-button type="primary" @click="saveViolation()">添加</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="model-title" style="margin-top: 25px;"> 执法依据信息 </div>
      <el-table :data="formData['illegalGistList']" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column prop="lawsName" label="法律名称" align="center" />
        <el-table-column prop="gistTypeText" label="依据分类" align="center" />
        <el-table-column prop="stripText" label="条" align="center" width="140" />
        <el-table-column prop="fundText" label="款" align="center" width="140" />
        <el-table-column prop="itemText" label="项" align="center" width="140" />
        <el-table-column prop="content" label="内容" align="center" />
      </el-table>
    </el-form>
  </div>
</template>
<style lang="scss" scoped>
.model-title {
  font-size: 16px;

  margin: 10px 0px 20px 0px;
}
.case-information {
  padding: 10px;
  .information-item {
    padding: 10px 40px;
  }
}
.uploder {
  :deep(.el-form-item__content) {
    height: 100%;
    .uq-uploader-ui {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
