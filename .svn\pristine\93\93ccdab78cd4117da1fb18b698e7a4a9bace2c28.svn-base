<script setup lang="ts">
defineOptions({ name: 'StatisticsAnalysis' })
import { pieCaseTypeReconsider, pieDecideTypeReconsider, listOfficeAnalyse, yoyCaseTypeReconsider, yoyDecideTypeReconsider } from '../../api/analysisChart'
import * as echarts from 'echarts'
const initYear = ref(new Date().getFullYear() + '')
const fieldInvolvement = ref<HTMLElement | null>(null)
const trialResults = ref<HTMLElement | null>(null)
const caseNumber = ref<HTMLElement | null>(null)
const resultCompared = ref<HTMLElement | null>(null)
interface TableItem {
  name: string
  value: number
}
const tableList = ref<TableItem[]>([])
const pieChart = (chart: any, val: any) => {
  if (chart) {
    const myChart = echarts.init(chart)
    const option = {
      title: {
        text: '案件数量\n' + val.totalValue,
        left: '30%',
        top: '50%',
        textAlign: 'center',
        textVerticalAlign: 'middle',
        textStyle: {
          color: 'rgb(0 0 0 / 43%)',
          fontWeight: 'normal',
          lineHeight: 30,
          fontSize: 18,
          rich: {
            score: {
              fontSize: 28,
              lineHeight: 99,
              fontWeight: 'bold',
              color: '#333',
              padding: [10, 0, 0, 0]
            }
          }
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        formatter: (value: any) => {
          const fieldItem = val.dataList.find((e: any) => e.name === value)
          if (fieldItem) {
            return `{title|${value}}{value|${fieldItem?.value ?? 0}}{percentage|${fieldItem?.ratio || 0}%}`
          } else {
            return `{title|${value}}`
          }
        },
        textStyle: {
          rich: {
            title: {
              fontSize: 12,
              padding: [0, 10, 0, 0],
              watch: 100,
            },
            value: {
              fontSize: 12,
              padding: [0, 10, 0, 0],
              watch: 80,
            },
          }
        },
        top: 'center',
        left: '50%',
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['30%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          data: val.dataList,
        },
      ],
    }
    myChart.setOption(option)
  }
}
const barChart = (chart: any, val: any, name: any) => {
  if (chart) {
    const myChart = echarts.init(chart)
    const option = {
      grid: {
        top: '20%',
        bottom: '14%' //也可设置left和right设置距离来控制图表的大小
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true
          }
        }
      },
      legend: {
        data: val.legendList,
        top: '8%',
        textStyle: {
          color: '#666'
        }
      },
      xAxis: {
        data: val.xaxisList,
        axisLine: {
          show: true, //隐藏X轴轴线
          lineStyle: {
            color: '#ccc'
          }
        },
        axisTick: {
          show: false //隐藏X轴刻度
        },
        axisLabel: {
          show: true,
          interval: 0, //强制显示所有x轴文字
          formatter: (value: any) => {
            if (name === 'caseNumber') {
              return value
            } else {
              let str = ''
              const result = value.substring(0, 7)
              const list = result.split('')
              list.map((item: any, index: any) => {
                const idx = index + 1
                if (idx % 4 === 0) {
                  str += item + '\n'
                } else {
                  str += item
                }
              })
              // 如果原始字符串长度大于10，添加省略号
              if (value.length > 8) {
                str += '...'
              }
              return str
            }
          },
          textStyle: {
            color: '#666' //X轴文字颜色
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '（个）',
          minInterval: 1,
          nameTextStyle: {
            color: '#666'
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666'
            }
          }
        },
        {
          type: 'value',
          name: '（%）',
          nameTextStyle: {
            color: '#666'
          },
          position: 'right',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#666'
            }
          }
        }
      ],
      series: [
        {
          name: val.legendList[0],
          type: 'bar',
          barWidth: 20,
          itemStyle: {
            normal: {
              color: val.colorList[0]
            }
          },
          data: val.prevValueList
        },
        {
          name: val.legendList[1],
          type: 'bar',
          barWidth: 20,
          itemStyle: {
            normal: {
              color: val.colorList[1]
            }
          },
          data: val.valueList
        },
        {
          name: val.legendList[2],
          type: 'line',
          yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
          smooth: true, //平滑曲线显示
          showAllSymbol: true, //显示所有图形。
          symbol: 'circle', //标记的图形为实心圆
          symbolSize: 10, //标记的大小
          itemStyle: {
            //折线拐点标志的样式
            color: val.colorList[2]
          },
          lineStyle: {
            color: val.colorList[2]
          },
          areaStyle: {
            color: 'rgba(5,140,255, 0.2)'
          },
          data: val.yoyList
        }
      ]
    }
    myChart.setOption(option)
  }
}
const yearClick = (val: any) => {
  listOfficeAnalyse({ year: val, limit: 10 }).then((res: any) => {
    tableList.value = res
  })
  pieCaseTypeReconsider({ data: val }).then((res: any) => {
    pieChart(fieldInvolvement.value, res)
  })
  pieDecideTypeReconsider({ data: val }).then((res: any) => {
    pieChart(trialResults.value, res)
  })
  yoyCaseTypeReconsider({ data: val }).then((res: any) => {
    res.colorList = ['#fa709a', '#96e6a1', '#058cff']
    barChart(caseNumber.value, res, 'caseNumber')
  })
  yoyDecideTypeReconsider({ data: val }).then((res: any) => {
    res.colorList = ['#00c6fb', '#3cba92', '#fee140']
    barChart(resultCompared.value, res, 'resultCompared')
  })
}

onMounted(() => {
  yearClick(initYear.value)
})
</script>

<!-- 行政复议分析 -->
<template>
  <div class="statistics-analysis">
    <uq-date
      class="year-picker" v-model="initYear" label="年份" type="year" @change="yearClick" value-format="YYYY"
      format="YYYY" placeholder="请选择年份" />
    <!-- <el-date-picker v-model="initYear" type="year" @change="yearClick" value-format="YYYY" format="YYYY" placeholder="请选择年份" /> -->
    <div class="analysis-nav">
      <div class="chart-top">
        <div class="item-chart">
          <p class="chart-title">按涉案处室分析</p>
          <div class="chart-list">
            <div v-for="(item, index) in tableList" class="office-item" :key="index">
              <span class="item-num">{{ index+1 }}</span>
              <span class="item-name">{{ item.name }}</span>
              <span class="item-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
        <div class="item-chart">
          <p class="chart-title">按涉案领域分析</p>
          <div class="chart" ref="fieldInvolvement" />
        </div>
        <div class="item-chart">
          <p class="chart-title">按审理结果分析</p>
          <div class="chart" ref="trialResults" />
        </div>
      </div>
      <div class="chart-bottom">
        <div class="item-chart">
          <p class="chart-title">案件数量同比分析</p>
          <div class="chart" ref="caseNumber" />
        </div>
        <div class="item-chart">
          <p class="chart-title">审理结果同比分析</p>
          <div class="chart" ref="resultCompared" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.statistics-analysis {
  height: 100%;
  width: 100%;

  .year-picker {
    width: 200px;
  }

  .analysis-nav {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    /* 确保子元素之间的间距均匀 */
    /* 子元素之间的间距 */
    height: calc(100% - 40px);
    flex-wrap: wrap;

    .item-chart {
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 4px;
      border: 1px solid #ebebeb;
      /* 可选：添加边框以便更清晰地看到布局 */
      box-sizing: border-box;

      /* 确保 padding 不影响宽度计算 */
      .chart-title {
        background: #F0F0FF;
        padding: 10px;
        font-size: 16px;
        width: 100%;
        font-weight: 600;
      }

      .chart {
        width: 100%;
        height: calc(100% - 40px);
      }
    }

    .chart-top {
      width: 100%;
      display: flex;
      gap: 10px;
      height: calc(50% - 5px);
       .item-chart:nth-child(1){
        width: calc(20% - 5px);
        .chart-list{
          height: 100%;
          width: 100%;
          .office-item {
            height: 10%;
            display: flex;
            align-items: center;
            padding: 0px 20px;

            &:nth-child(1) .item-num,&:nth-child(2) .item-num,&:nth-child(3) .item-num{
              color: #ffffff;
              background-color: #314659;
            }
          }
          .item-num {
            width: 17px;
            height: 17px;
            margin-right: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: #EFF2F5;

          }
          .item-name {
            width: calc(100% - 80px);
          }
          .item-value {
            text-align: center;
            width: 30px;
          }

        }
       }
      .item-chart {
        height: calc(100% - 5px);
        width: calc(40% - 5px);
      }
    }

    .chart-bottom {
      width: 100%;
      display: flex;
      gap: 10px;
      height: calc(50% - 5px);

      .item-chart {
        height: calc(100% - 5px);
        width: calc(50% - 5px);

      }
    }
  }
}
</style>
