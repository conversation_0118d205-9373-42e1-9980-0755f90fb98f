<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>北京市发展改革部门行政执法业务综合管理平台</title>
  <script type="text/javascript" src="/wps/wpsjsrpcsdk.js"></script>
  <script type="text/javascript">
    /*
    * 将自己的加载项地址配置到这里来
    * 需要保证加载项的name和业务业务系统中传递加载项name相对应
    * url必须以/ 结尾，且url+ribbon.xml和url+index.html在清除浏览器缓存的情况下能直接访问，不会被重定向
    * addonType:对应组件类型，wps文字，wpp演示，et表格
    */
    const wpsJsApiPluginsUrl = `${window.location.origin}/wps/WpsOAAssist_1.1.7.7z`// 离线模式配置参考
    const curList = [{ 'name': 'WpsOAAssist', 'addonType': 'wps', 'online': 'false', 'url': wpsJsApiPluginsUrl, 'version': '1.1.7' }]
    // let localList = []
    let publishIndex = 0
    let publishUnIndex = 0
    /* 获取用户本地全部加载项的接口是必须要的，这个接口做了判断，
    ** 如果58890端口未启动，会先去启动这个端口
    */
    // 加载项安装函数
    function installWpsAddin(callBack) {
      wpsAddonMgr.getAllConfig(e => {
        // if (!e.response || e.response.indexOf('null') >= 0) { // 本地没有加载项，直接安装
        //   if (curList.length > 0) {
        //     installWpsAddinOne(callBack)
        //   }
        // } else { // 本地有加载项，先卸载原有加载项，然后再安装
        //   localList = JSON.parse(e.response)
        //   unInstallWpsAddinOne(callBack)
        // }
        unInstallWpsAddinOne(callBack)
      })
    }
    function getChild() {
      let projectDom = document.getElementById('project')
      return projectDom ? projectDom.childNodes : null
    }
    let timer = null
    // 安装单个加载项
    function installWpsAddinOne(callBack) {
      clearTimeout(timer)
      wpsAddonMgr.enable(curList[publishIndex], e => {
        if (e.status) {
          console.log(e.msg)
        } else {
          console.log('安装成功')
          let child = getChild()
          if (!child || child.length === 0) {
            timer = setTimeout(() => {
              child = getChild()
              if (!child || child.length === 0) {
                window.location.reload()
                clearTimeout(timer)
              } else {
                clearTimeout(timer)
              }
            }, 5000)
          } else {
            clearTimeout(timer)
          }
        }
        publishIndex++
        if (publishIndex < curList.length) {
          installWpsAddinOne()
        } else {
          callBack && callBack()
        }
      })
    }
    // 卸载单个加载项
    function unInstallWpsAddinOne(callBack) {
      wpsAddonMgr.disable(curList[publishUnIndex], e => {
        if (e.status) {
          console.log(e.msg)
        } else {
          console.log('卸载成功')
        }
        publishUnIndex++
        if (publishUnIndex < curList.length) {
          unInstallWpsAddinOne(callBack)
        } else {
          if (curList.length > 0) {
            installWpsAddinOne(callBack)
          }
        }
      })
    }
    // 初始化调用方法
    installWpsAddin(() => {
      wpsInvoke.RegWebNotify(wpsInvoke.ClientType.wps, 'WpsOAAssist', message => {
        if (typeof message === 'string') {
          const msgArr = message.split(',')
          console.log(msgArr)
        }
        timer = setTimeout(() => {
          child = getChild()
          if (!child || child.length === 0) {
            window.location.reload()
            clearTimeout(timer)
          } else {
            clearTimeout(timer)
          }
        }, 2000)
      })
    })
  </script>
</head>

<body>
  <div id="project"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>