<script setup lang="ts">
import { uqConfirm } from '@uq/base/hooks'
import { openCaseBasicForm, openAjForm, saveCaseBasic, handleCase } from '../../../api/administrativePenalty'
const CaseInformation = defineAsyncComponent(() => import('../ModuleStep/CaseInformation.vue')) // 案件基本信息
const EnforcementDocumentsSH = defineAsyncComponent(() => import('../ModuleStep/EnforcementDocumentsSH.vue')) // 执法文书
const router: any = inject('router')
import { ElMessage } from 'element-plus'

import { ref } from 'vue'
defineOptions({ name: 'RegistrationRegisterForm' })
// const myForm = ref<InstanceType<typeof FormInstance>>()
interface Props {
  type: string
  id: string,
  openType: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'VIEW',
  id: '',
  openType: ''
})

const caseId = ref(props.id)

watch(() => props.id, (val) => {
  caseId.value = val
})

const initData = ref()
const emit = defineEmits(['phaseTabList', 'step', 'update:id', 'close'])
const init = () => {
  if (props.type === 'VIEW') {
    openAjForm({ data: caseId.value }).then((res: any) => {
      emit('phaseTabList', res.phaseTabList)
      res.buttonList.push({ id: 'back', name: props.openType === 'dialog' ? '关闭' : '返回' })
      res.casePartyLegal = res.casePartyLegal || {}
      initData.value = res
    })
  } else {
    openCaseBasicForm({ data: caseId.value }).then((res: any) => {
      emit('phaseTabList', res.phaseTabList)
      res.buttonList.push({ id: 'back', name: props.openType === 'dialog' ? '关闭' : '返回' })
      res.casePartyLegal = res.casePartyLegal || {}
      initData.value = res
    })
  }
}
init()
const activeNames = ref(['1', '7'])

type ChildFormExposed = {
  validateForm: () => Promise<boolean>
  getFormData: () => { [key: string]: any }
}

// 定义子表单的引用
const childFormRef = ref<ChildFormExposed>()
const handleClick = (btn: any) => {
  if (btn.id === 'back') {
    if (props.openType === 'dialog') {
      emit('close')
    } else {
      router.back()
    }
  } else if (btn.id === 'PREV') { //上一步
    emit('step', { type: 'prev', casePhaseCode: null })
  } else if (btn.id === 'NEXT') { //下一步
    emit('step', { type: 'next', casePhaseCode: null })
  } else {
    handleSubmit(btn.id)
  }
}
// 提交表单
const handleSubmit = async (btnId: string) => {
  if (!childFormRef.value) return
  const formData = childFormRef.value.getFormData()
  const obj = {
    caseBasic: formData._value.caseBasic,
    casePartyLegal: formData._value.casePartyLegal,
    casePowerList: formData._value.casePowerList
  }
  if (btnId === 'SAVE') {
    saveCaseBasic(obj).then((res:any) => {
      emit('update:id', res)
      ElMessage.success('保存成功！')
      openCaseBasicForm({ data: res }).then((res: any) => {
        emit('phaseTabList', res.phaseTabList)
        res.buttonList.push({ id: 'back', name: '返回' })
        res.casePartyLegal = res.casePartyLegal || {}
        initData.value = res
      })
    })
  } else if (btnId === 'COMPLETE') {
    // 验证子表单
    const isValid = await childFormRef.value.validateForm()
    if (!formData._value.casePowerList || formData._value.casePowerList.length === 0 || !formData._value.casePowerList[0].powerId) return ElMessage.warning('请选择执法职权！')
    if (isValid) {
      uqConfirm('确认立案信息已登记完成？', () => {
        handleCase(obj).then((res: any) => {
          ElMessage.success('办理成功！')
          emit('step', { type: 'next', casePhaseCode: null, changeType: 'handle', caseId: res })
        })
      })
    // 这里执行API提交等后续操作
    } else {
      ElMessage.error('表单验证失败，请检查输入')
    }
  }
}

</script>
<!-- 登记立案 -->
<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item title="案件基本信息" name="1">
      <CaseInformation ref="childFormRef" v-if="initData" :initData="{ ...initData, id: caseId,source: 'city'}"  />
    </el-collapse-item>
    <el-collapse-item title="执法文书" name="7">
      <EnforcementDocumentsSH v-if="initData" @refresh="init()" :initData="{...initData,id: caseId}" />
    </el-collapse-item>
  </el-collapse>
  <div class="uq-center mt-15">
    <button-group :group="false" size="default" :modelValue="initData?.buttonList" @onClick="handleClick" />
  </div>
</template>

<style lang="scss" scoped>
.el-collapse {
  flex: 1;
  overflow-y: auto;
}
</style>
