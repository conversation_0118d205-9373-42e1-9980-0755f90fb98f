<script setup lang="ts">
// 立案登记办理信息
import { downLoadFile } from '@uq/base/hooks'
defineOptions({ name: 'RegistrationHandle' })
import { FormInstance } from 'element-plus'
import { ref, watch } from 'vue'
interface Props {
  id: string
  initData: any
}
const props = withDefaults(defineProps<Props>(), {
  initData: {},
  id: ''
})
watch(
  () => props.initData.recordCountyVO,
  (val: any) => {
    resData.value = props.initData
    formData.value = val || {}
  },
  { deep: true }
)
// 修改 formData 的类型定义
type SimpleObject = Record<string, any>
const formData = ref<SimpleObject>(props.initData.recordCountyVO || {})
const resData = ref(props.initData || {})
const formRef = ref<FormInstance>()
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch {
    return false
  }
}
const downloadTemplate = () => {
  if (!formData.value.docId) {
    ElMessage({ message: '需先保存立案信息！', type: 'warning' })
  } else {
    downLoadFile(formData.value.docId)
  }
}
// 暴露数据获取方法
const getFormData = () => ({ ...formData })
// 暴露给父组件的方法清单
defineExpose({
  validateForm,
  getFormData
})
</script>
<!-- PunishCity/AdministrativePenalty -->
<!-- 案件基本信息 -->
<template>
  <div class="case-information">
    <el-form class="uq-form" ref="formRef" :model="formData" size="large" :disabled="!resData.formRight" label-position="right" label-width="150px">
      <uq-input v-model="formData['operateOpinion']" label="执法人员意见" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入执法人员意见" prop="operateOpinion" rules="required" />
      <el-row>
        <el-col :span="8">
          <uq-input v-model="formData['operateName']" label="经办人"  placeholder="请输入经办人" prop="operateName"  rules="required" />
        </el-col>
        <el-col :span="8">
          <uq-input v-model="formData['operatePhone']" label="经办人联系方式"  placeholder="请输入经办人联系方式" prop="operatePhone" rules="required|isPhoneMobileZhbg" />
        </el-col>
        <el-col :span="8">
          <uq-date v-model="formData['operateOpinionTime']" label="申请立案日期"  placeholder="请选择申请立案日期" prop="operateOpinionTime" rules="required" />
        </el-col>
      </el-row>
      <uq-input v-model="formData['leaderOpinion']" label="领导审核意见" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"  placeholder="请输入领导审核意见" prop="leaderOpinion" rules="required" />
      <el-row>
        <el-col :span="8">
          <uq-input v-model="formData['leaderName']" label="审核人"  placeholder="请输入审核人" prop="leaderName" rules="required" />
        </el-col>
        <el-col :span="8">
          <uq-input v-model="formData['leaderPhone']" label="审核人联系方式"  placeholder="请输入审核人联系方式" prop="leaderPhone" rules="required|isPhoneMobileZhbg" />
        </el-col>
        <el-col :span="8">
          <uq-date v-model="formData['leaderOpinionTime']" label="批准立案日期"  placeholder="请选择批准立案日期" prop="leaderOpinionTime" rules="required" />
        </el-col>
      </el-row>
      <el-form-item label="文书类型">
        <el-radio-group v-model="formData['templateType']" >
          <el-radio v-for="(item, index) in resData.templateTypeList" :key="index" :value="item.dmValue" class="radio-class">
            <span class="name">{{ item.dmName }}</span>
            <el-button v-if="item.dmValue==='LASPB' && formData['templateType']==='LASPB'" type="primary" link @click="downloadTemplate">立案审批表模板</el-button>
            <uq-input v-else-if="item.dmValue==='100qt' &&  formData['templateType']==='100qt'" v-model="formData['templateName']" label="" label-width="0px" bottom="0px" placeholder="请输入文书类型/文书名"  />
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <uq-input v-model="formData['docNum']" label="文号"  placeholder="请输入文号" prop="docNum" rules="" />
        </el-col>
        <el-col :span="12">
          <uq-date v-model="formData['docEffectDate']" label="文书生效日期"  placeholder="请选择文书生效日期" prop="docEffectDate" rules="" />
        </el-col>
      </el-row>
      <el-form-item class="uploder" label="上传附件" rule="required">
        <chunk-uploader v-model="formData['fileList']" :fileParams="{ instanceId: formData.id, docUser: 'uqian', docType:'upload', docDir:'hlwjjg/punish' }" :disabled="!formData.formRight" />
      </el-form-item>
    </el-form>
  </div>
</template>
<style lang="scss" scoped>
.model-title {
  font-size: 16px;
  margin: 10px 0px 20px 0px;
}
.case-information {
  padding: 10px;
  .information-item {
    padding: 10px 40px;
  }
}
.uploder {
  :deep(.el-form-item__content) {
    height: 100%;
    .uq-uploader-ui {
      height: 100%;
      width: 100%;
    }
  }
}
.radio-class {
  width: 100%;
  :deep(.el-radio__label) {
    display: inline-flex;
    .name {
      display: inline-block;
      width: 100px;
      margin-right: 10px;
    }
  }
}
</style>
