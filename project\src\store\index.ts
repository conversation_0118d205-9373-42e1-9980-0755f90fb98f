import { defineStore } from 'pinia'

// 从 sessionStorage 获取保存的状态
const getStoredState = () => {
  try {
    const stored = sessionStorage.getItem('projectStore')
    return stored ? JSON.parse(stored) : {}
  } catch {
    return {}
  }
}

export const projectStore = defineStore('projectStore', {
  state: () => {
    const stored = getStoredState()
    return {
      currentLoginDate: stored.currentLoginDate || null, // 待办总数
      enforcementDocuments: stored.enforcementDocuments || [], // 执法文书
      topCurrentMenu: stored.topCurrentMenu || {}, // 顶部菜单
      menuList: stored.menuList || [], // 菜单列表,
      breadcrumbList: stored.breadcrumbList || [], // 面包屑列表
      currentMenuUrl: stored.currentMenuUrl || '', // 当前菜单url
      currentLeftMenu: stored.currentLeftMenu || {}, // 左侧菜单激活
      pageJump: stored.pageJump || false // 页面跳转
    }
  },
  //适合计算属性 顾名思义 依赖于state的值计算出来的属性
  getters: {
    // leftActiveMenuId: (state) => {
    //   return state.leftActiveMenu?.menuId
    // }
  },
  //适合定义业务逻辑
  actions: {
    // 保存状态到 sessionStorage
    saveToStorage() {
      try {
        const state = {
          currentLoginDate: this.currentLoginDate,
          enforcementDocuments: this.enforcementDocuments,
          topCurrentMenu: this.topCurrentMenu,
          menuList: this.menuList,
          breadcrumbList: this.breadcrumbList,
          currentMenuUrl: this.currentMenuUrl,
          currentLeftMenu: this.currentLeftMenu,
          pageJump: this.pageJump
        }
        sessionStorage.setItem('projectStore', JSON.stringify(state))
      } catch (error) {
        console.warn('Failed to save state to sessionStorage:', error)
      }
    },
    setCurrentLoginDate(date: string | undefined) {
      this.currentLoginDate = date
      this.saveToStorage()
    },
    setEnforcementDocuments(list: any) {
      // 根据 templateType 去重
      if (Array.isArray(list)) {
        const map = new Map()
        list.forEach((item: any) => {
          if (item && item.templateType !== undefined) {
            map.set(item.templateType, item)
          }
        })
        this.enforcementDocuments = Array.from(map.values())
      } else {
        this.enforcementDocuments = []
      }
      this.saveToStorage()
    },
    setTopCurrentMenu(menu: any) {
      this.topCurrentMenu = menu
      this.saveToStorage()
    },
    setMenuList(list: any) {
      this.menuList = list
      this.saveToStorage()
    },
    setCurrentMenuUrl(url: string) {
      this.currentMenuUrl = url
      this.saveToStorage()
    },
    setCurrentLeftMenu(menu: any) {
      this.currentLeftMenu = menu
      this.saveToStorage()
    },
    setPageJump(status: boolean) {
      this.pageJump = status
      this.saveToStorage()
    },
    setBreadcrumbList(menu: any) {
      const exists = this.breadcrumbList.some((item: any) => item.menuId === menu.menuId)
      if (!exists) {
        this.breadcrumbList = [...this.breadcrumbList, menu]
        this.saveToStorage()
      }
    },
    // 清除面包屑
    clearBreadcrumbList() {
      this.breadcrumbList = []
      this.saveToStorage()
    },
    // 根据当前路由初始化菜单状态
    initMenuStateFromRoute(currentRoute: string) {
      if (!currentRoute || currentRoute === '/') return

      // 查找菜单路径
      const findMenuPath = (menus: any[], url: string): any[] => {
        for (const menu of menus) {
          if (menu.menuUrl === url) {
            return [menu]
          }
          if (menu.children && menu.children.length > 0) {
            const childPath = findMenuPath(menu.children, url)
            if (childPath.length > 0) {
              return [menu, ...childPath]
            }
          }
        }
        return []
      }

      if (this.menuList.length > 0) {
        const menuPath = findMenuPath(this.menuList, currentRoute)
        if (menuPath.length > 0) {
          // 设置顶部菜单
          this.setTopCurrentMenu(menuPath[0])
          // 设置当前菜单URL
          this.setCurrentMenuUrl(currentRoute)
          // 如果有二级菜单，设置左侧菜单
          if (menuPath.length > 1) {
            this.setCurrentLeftMenu(menuPath[1])
          }
          // 设置面包屑
          const targetMenu = menuPath[menuPath.length - 1]
          this.setBreadcrumbList(targetMenu)
        }
      }
    }
  }
})
