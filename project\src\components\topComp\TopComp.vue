<script setup lang='ts'>
defineOptions({ name: 'TopComp' })
import { uqConfirm, openHtml } from '@uq/base/hooks'
import { logout } from '@/api/home.ts'

import { useRouter } from 'vue-router'
const router = useRouter()

import { projectStore } from '../../store'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { topCurrentMenu, pageJump } = storeToRefs(store)

const props = withDefaults(
  defineProps<{
    menuList: any[]
    userInfo: any
  }>(), {}
)

const emit = defineEmits(['update:leftMenuList'])

const currentMenu = ref()

// 监听菜单列表变化，初始化当前菜单
watch(() => props.menuList, (newMenuList) => {
  if (newMenuList && newMenuList.length > 0) {
    // 如果没有保存的顶部菜单，设置第一个为默认
    if (!topCurrentMenu.value || !topCurrentMenu.value.menuId) {
      currentMenu.value = newMenuList[0]
    } else {
      currentMenu.value = topCurrentMenu.value
    }
  }
}, { immediate: true })

// 标记是否正在初始化，避免循环
const isInitializing = ref(false)

watch(() => topCurrentMenu.value, (val: any) => {
  if (val && val.menuId && !isInitializing.value) {
    currentMenu.value = val
    if (pageJump.value) {
      menuClick(val)
      store.setPageJump(false) // 重置 pageJump 状态
    } else {
      // 更新左侧菜单列表，但不触发路由跳转
      if (val.menuUrl === '/HomePage') {
        emit('update:leftMenuList', '/HomePage')
      } else {
        emit('update:leftMenuList', val?.children || [])
      }
    }
  }
}, { deep: true })
const unReadCount = ref(0)
const menuClick = (item: any) => {
  // 防止循环调用
  isInitializing.value = true

  // 先初始化左侧菜单
  if (item.menuUrl === '/HomePage') {
    emit('update:leftMenuList', '/HomePage')
    // 只有当前路由不是 /HomePage 时才跳转
    if (router.currentRoute.value.path !== '/HomePage') {
      router.push(item.menuUrl)
    }
    // 设置首页的 currentMenuUrl
    store.setCurrentMenuUrl('/HomePage')
  } else {
    // 这里加一个深拷贝，确保 emit 的值每次都不同，触发响应
    emit('update:leftMenuList', JSON.parse(JSON.stringify(item?.children || [])))
    // 只有在用户主动点击时才清空当前菜单URL，显示三级菜单
    // 如果当前有具体的路由，不要清空
    const currentPath = router.currentRoute.value.path
    if (currentPath === '/' || currentPath === '/HomePage' || !store.currentMenuUrl) {
      store.setCurrentMenuUrl('')
    }
  }

  // 更新状态
  store.setTopCurrentMenu(item)
  store.setPageJump(false)
  window.sessionStorage.removeItem('searchData')

  // 重置初始化标记
  nextTick(() => {
    isInitializing.value = false
  })
}
// const getUnReadCount = () => {
//   countPageMessage({
//     isPage: true,
//     queryParam: {
//       isRead: 0
//     }
//   }).then(res => {
//     unReadCount.value = res.total
//   })
// }
// 退出登录
const logoutFun = () => {
  uqConfirm('确定退出吗？', () => {
    logout({ isLocalLogin: window.sessionStorage.getItem('login'), cityFlag: window.sessionStorage.getItem('cityFlag') }).then((r: any) => {
      const loginUrl: string | null = r?.toUrl === 'login' ? '/login' : r?.toUrl
      openHtml({ url: loginUrl })
      window.sessionStorage.clear()
    })
  })
}
</script>

<template>
  <div class="top-comp">
    <img class="logo" src="../../assets/images/logo.png" alt="">
    <div class="top-menu">
      <div :class="{active: topCurrentMenu.menuId === item.menuId}" @click="menuClick(item)" class="top-meun-item" v-for="item in menuList" :key="item.menuId">{{ item.menuName }}</div>
    </div>
    <el-popover popper-class="message-popper" placement="bottom" width="350" trigger="click">
      <MessageList @getUnReadCount="(count: number) => unReadCount = count" />
      <template v-slot:reference>
        <div  class="fc flr msg-pop">
          <i style="color: #fff;" class="uq-iconfont icon-uq_notify" />
          <div v-if="unReadCount>0" class="d">●</div>
          <!-- <div v-if="unReadCount>0" class="un-read">{{ unReadCount }}</div> -->
        </div>
      </template>
    </el-popover>
    <div class="user">
      <p>欢迎您：{{ userInfo.userName }}</p>
      <img @click="logoutFun" src="../../assets/images/logout.png" alt="">
    </div>
  </div>
</template>

<style lang='scss' scoped>
.fc {
  justify-content: center;
  align-items: center;
  justify-items: center;
  align-content: center;
}
.flr {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
}
.msg-pop {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    height: 100%;
    position: relative;
    i {
      font-size: 20px;
    }
  }
  .un-read {
    position: absolute;
    padding: 0 10px;
    border-radius: 10px;
    background: #f5222d;
    height: 18px;
    line-height: 18px;
    top: 8px;
    transform: translateX(50%);
  }
  > div {
    padding: 0 10px;
    margin: 0 2px;
    cursor: pointer;
    &.logout:hover {
      background-color: rgb(215, 43, 3);
      color: #ffffff;
    }
    > i {
      font-size: 20px;
    }
  }
.top-comp{
  padding: 25px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .logo{
    width: 648px;
  }
  .top-menu{
    padding-left: 100px;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;
    .top-meun-item{
      color: #fff;
      font-size: 20px;
      cursor: pointer;
      &::after{
        content: '';
        display: inline-block;
        height: 5px;
        width: 100%;
        border-radius: 5px;
      }
      &.active::after{
        background-color: #fff;
      }
    }
  }
  .user{
    display: flex;
    align-items: center;
    gap: 11px;
    p{
      font-size: 16px;
      color: #fff;
    }
    img{
      width: 41px;
      height: 41px;
      cursor: pointer;
    }
  }
}
.d{
  color: #ee0a24;
  font-size: 10px;
  margin-top: -15px;
}
</style>
