<script setup lang="ts">
defineOptions({ name: 'LawEnforcementTaskSearch' })
const router:any = inject('router')

const goFun = (args: any) => {
  router.push({
    name: 'LawEnforcement_CheckList',
    params: {
      id: JSON.stringify({
        sourcePageType: 'zhcx', //来源页面类型 zhcx: 综合查询 db: 待办任务 zb: 在办任务 yb: 已办任务
        source: '4', //来源 1：待办任务 2：在办任务 3：已办任务 4: 执法任务数据综合查询
        taskType: args.taskType, //任务类型 01在线 02互动 03现场
        basisId: args.basisId, //大厅依据id
        formCode: args.formCode, //表单编码
        taskId: args.taskId, //任务id
        checkType: args.checkType, //业务领域
        checkRole: args.checkRole, // 检察人员类型
        processInstanceId: args.processInstanceId, //任务流程实例id
        year: args.year, //年份
        classification: args.classification, // 市级:1；或区级:2
        currentNodeCode: args.currentNodeCode//业务环节编码
      })
    }
  })
}
</script>
<!-- 执法任务数据综合查询 -->
<template>
  <common-form code="LAW_ENFORCEMENT_TASK_SEARCH"  @handle="goFun" />
</template>
