<script setup lang="ts">
defineOptions({ name: 'Project' })
import MenuComp from './components/menuComp'
import TopComp from './components/topComp'
import MainMenu from './components/mainMenu'
import { useRouter, useRoute } from 'vue-router'

const route = useRoute()
const router = useRouter()

import { projectStore } from './store'
const store = projectStore()
import { storeToRefs } from 'pinia'
const { currentMenuUrl, pageJump, breadcrumbList } = storeToRefs(store)

import { listAllMenuForSecurity } from './api/home'

const leftMenuList = ref() //左侧二级菜单
const thirdMenuList = ref() //三级菜单

const menuList = ref()
const userInfo = ref()
listAllMenuForSecurity({ menuCode: 'ZFPT' }).then((res: any) => {
  store.setMenuList(res.menus)
  store.setTopCurrentMenu(res.menus[0])
  menuList.value = res.menus
  userInfo.value = res.user
  window.sessionStorage.setItem('userLoginName', res.user.userLoginName)
  if (menuList.value[0].menuUrl === '/HomePage'){
    leftMenuList.value = '/HomePage'
    router.push('/HomePage')
  }
})

// const breadcrumbList = ref<any[]>([]) //面包屑

// const currentMenuUrl = ref()

// const currentMenu = ref() //当前选中的菜单
const menuclick = (menu: any) => {
  if (menu.menuUrl) {
    breadcrumbChange(menu)
    store.setCurrentMenuUrl(menu.menuUrl)
    router.push(menu.menuUrl)
    window.sessionStorage.removeItem('searchData')
  }
}

const breadcrumbChange = (menu: any) => {
  if (breadcrumbList.value.indexOf(menu) === -1) {
    breadcrumbList.value.push(menu)
  }
}

// 点击面包屑
const breadcrumbClick = (menu: any) => {
  store.setCurrentMenuUrl(menu.menuUrl)
  router.push(menu.menuUrl)
  window.sessionStorage.removeItem('searchData')
}

// 删除面包屑
const deleteBreadcrumb = (menuUrl: any) => {
  const index = breadcrumbList.value.findIndex((item: { menuUrl: string }) => item.menuUrl === menuUrl)
  if (menuUrl === currentMenuUrl.value) {
    if (index === 0) {
      if (breadcrumbList.value.length > 1) {
        store.setCurrentMenuUrl( breadcrumbList.value[index + 1].menuUrl)
        router.push(breadcrumbList.value[index + 1].menuUrl)
        breadcrumbList.value.splice(index, 1)
      }
    } else {
      store.setCurrentMenuUrl(breadcrumbList.value[index - 1].menuUrl)
      router.push(breadcrumbList.value[index - 1].menuUrl)
      breadcrumbList.value.splice(index, 1)
    }
  } else {
    breadcrumbList.value.splice(index, 1)
  }
  window.sessionStorage.removeItem('searchData')
}
const leftMenuClick = () => {
  if (!pageJump.value){
    store.setCurrentMenuUrl('')
  }
}
provide('router', router) // 定义路由 所有页面都能用路由
provide('route', route) // 定义本页路由 所有页面都能用路由
</script>

<template>
  <div class="project-layout">
    <el-container>
      <el-header height="auto">
        <TopComp v-model:leftMenuList="leftMenuList" v-if="userInfo" :menuList="menuList" :userInfo="userInfo" />
      </el-header>
      <el-container v-if="leftMenuList === '/HomePage'">
        <router-view />
      </el-container>
      <el-container v-show="leftMenuList !== '/HomePage'">
        <el-aside width="270px">
          <MenuComp @left-menu-click="leftMenuClick"  v-model:thirdMenuList="thirdMenuList" :menuList="leftMenuList || []" />
        </el-aside>
        <div class="breadcrumb">
          <img class="breadcrumb-bg breadcrumb-left" src="./assets/images/breadcrumb-left.png" alt="" >
          <div class="breadcrumb-bg breadcrumb-center" />
          <img class="breadcrumb-bg breadcrumb-right" src="./assets/images/breadcrumb-right.png" alt="" >
          <img class="address" src="./assets/images/address.png" alt="" >
          <div class="breadcrumb-list">
            <el-tabs v-model="currentMenuUrl" type="card" class="menu-tabs" :closable="breadcrumbList && breadcrumbList.length > 1" @tab-remove="deleteBreadcrumb">
              <el-tab-pane v-for="item in breadcrumbList" :key="item.menuUrl" :label="item.menuName" :name="item.menuUrl">
                <template #label>
                  <div class="tab-title menu-top" :class="currentMenuUrl === item['menuUrl'] ? 'menu-active' : ''" @click="breadcrumbClick(item)">
                    {{ `${item['menuPName'] ? item['menuPName'] + ' - ' + item['menuName'] : item['menuName']}` }}
                  </div>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <el-main>
          <div class="main-content">
            <MainMenu @menuclick="menuclick" :menuList="thirdMenuList || []" v-if="!currentMenuUrl" />
            <router-view v-slot="{ Component, route }" v-else>
              <component v-if="Component" :is="Component" :key="route.fullPath" />
            </router-view>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style lang="scss" scoped>
  .project-layout {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    background-image: url(./assets/images/top-banner.png);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    padding: 0 20px 20px;
    background-color: #e8f0fb;
    .el-aside {
      overflow: hidden;
    }
    .el-container {
      overflow-y: auto;
    }
    .el-header {
      padding: 0;
    }
    .breadcrumb {
      height: 50px;
      position: relative;
      display: flex;
      align-items: center;
      padding: 0 80px;
      gap: 10px;
      position: absolute;
      left: 260px;
      right: 20px;
      .address {
        width: 37px;
        height: 37px;
        z-index: 9;
      }
      .breadcrumb-list {
        width: calc(100% - 40px);
        .el-tabs {
          width: 100%;
          :deep {
            .el-tabs__header {
              margin-bottom: 0;
              border: none;
            }
            .is-scrollable {
              padding: 0 30px 0 20px;
            }
            .el-tabs__nav-prev,
            .el-tabs__nav-next {
              color: #fff;
              font-size: 16px;
              z-index: 9;
            }
            .el-tabs__nav {
              border: none;
            }
            .el-tabs__item {
              border: none;
              background: #3b8bff;
              color: #fff;
              margin-left: 10px;
              border-radius: 4px;
              padding-left: 0 !important;
              padding-right: 20px;
              &:first-child {
                margin-top: 0;
              }
              &.is-active {
                border: 1px solid #ffdd53;
                background: #1d82ff;
                color: #ffdd53;
              }
              .tab-title {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                padding-left: 20px;
              }
            }
          }
        }
      }
      .breadcrumb-bg {
        position: absolute;
        top: 0;
        bottom: 0;
        height: 50px;
        background-size: 100% 100% !important;
      }
      .breadcrumb-left {
        width: 70px;
        left: 0;
      }
      .breadcrumb-center {
        left: 70px;
        right: 70px;
        background: url(./assets/images/breadcrumb-center.png) repeat center center;
      }
      .breadcrumb-right {
        width: 70px;
        right: 0;
      }
    }
    .el-main {
      position: relative;
      box-sizing: border-box;
      padding: 0;
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      .main-content {
        padding: 20px;
        margin-top: 60px;
        background: #ffffff;
        border-radius: 10px;
        position: relative;
        box-sizing: border-box;
        overflow: hidden;
        flex: 1;
      }
    }
  }
</style>
