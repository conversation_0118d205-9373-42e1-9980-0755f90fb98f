<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
const RegistrationRegisterForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/RegistrationRegisterForm.vue')) // 登记立案
const InvestigateEvidenceForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/InvestigateEvidenceForm.vue')) // 调查取证
const CaseReviewForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/CaseReviewForm.vue')) // 案件审核
const PunishmentDecisionForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/PunishmentDecisionForm.vue')) // 处罚决定
const PunishmentExecutionForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/PunishmentExecutionForm.vue')) // 处罚执行
const CloseCaseForm = defineAsyncComponent(() => import('../../components/AdministrativePenalty/District/CloseCaseForm.vue')) // 结案
defineOptions({ name: 'AdministrativePenalty' })
import { inject, ref } from 'vue'

const route: any = inject('route')

const params = route.params.id ? JSON.parse(route.params.id) : {}

const caseId = ref(params.id || '')
console.log(caseId)

const activeName = ref(params.tabActive)
const tabList = ref([
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' },
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' },
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' },
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' },
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' },
  { casePhaseName: '', casePhaseTip: '', clickFlag: false, currentPhase: '', casePhaseCode: '' }
])
const stepClick = (tab: any, index: number) => {
  if (tab.clickFlag) activeName.value = index
}
const getPhaseTabList = (list: any) => {
  tabList.value = list
}
const step = (data: any) => {
  if (data.type === 'next') {
    if (data.caseId) caseId.value = data.caseId
    if (data.casePhaseCode){
      tabList.value.forEach((item: any, index: number) => {
        if (item.casePhaseCode === data.casePhaseCode) {
          activeName.value = index
        }
      })
    } else {
      if (data.changeType === 'handle'){
        activeName.value = activeName.value + 1
      } else {
      // 跳到下一个clickFlag为true的步骤
        let nextIndex = activeName.value + 1
        while (nextIndex < tabList.value.length && !tabList.value[nextIndex].clickFlag) {
          nextIndex++
        }
        if (nextIndex < tabList.value.length) {
          activeName.value = nextIndex
        }
      }
    }
  } else if (data.type === 'prev') {
    // 跳到上一个clickFlag为true的步骤
    let prevIndex = activeName.value - 1
    while (prevIndex >= 0 && !tabList.value[prevIndex].clickFlag) {
      prevIndex--
    }
    if (prevIndex >= 0) {
      activeName.value = prevIndex
    }
    // 如果没有可跳转的步骤则不变
  }
}
</script>
<!-- 行政处罚表单 -->
<template>
  <div class="administrative-penalty">
    <el-steps :active="activeName"  align-center>
      <el-step :class="{'not-allowed': !item.clickFlag,'cursor-pointer': item.clickFlag,'process': item.currentPhase === item.casePhaseCode, 'finish': Number(item.casePhaseCode) < Number(item.currentPhase)}" v-for="(item,index) in tabList" :key="index" :title="item.casePhaseName" :description="item.casePhaseTip" @click="stepClick(item, index)" />
    </el-steps>
    <!-- 登记立案 -->
    <registration-register-form  v-if="activeName === 0" v-model:id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
    <!-- 调查取证 -->
    <investigate-evidence-form v-else-if="activeName === 1" :id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
    <!-- 案件审核 -->
    <case-review-form v-else-if="activeName === 2" :id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
    <!-- 处罚决定 -->
    <punishment-decision-form v-else-if="activeName === 3" :id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
    <!-- 处罚执行 -->
    <punishment-execution-form v-else-if="activeName === 4" :id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
    <!-- 结案 -->
    <close-case-form v-else-if="activeName ===5 " :id="caseId" :type="params.type" openType="page" @phaseTabList="getPhaseTabList" @step="step" />
  </div>

</template>

<style lang="scss" scoped>
.administrative-penalty{
  height: 100%;
  display: flex;
  flex-direction: column;
  .flex-1{
    flex: 1;
    overflow-y: auto;
  }
  .el-steps{
    :deep(.finish){
      .el-step__head{
        border-color: #67c23a;
        color: #67c23a;
      }
      .el-step__title{
        color: #67c23a;
      }
      .el-step__description{
        color: #67c23a;
      }
    }
    :deep(.process){
      .el-step__head{
        border-color: #409eff;
        color: #409eff;
      }
      .el-step__title{
        color: #409eff;
      }
      .el-step__description{
        color: #409eff;
      }
    }
    :deep(.is-process){
      &.el-step__title {
        font-size: 18px;
      }
    }
  }
}
:deep{
  .el-collapse-item__header{
    font-weight: bold;
    font-size: 18px;
  }
}
.el-steps{
  margin-bottom: 20px;
  .el-step{
    &.cursor-pointer{
      cursor: pointer;
    }
    &.not-allowed{
      :deep(){
        .el-step__head{
          border-color: #a8abb2;
          color: #a8abb2;
        }
        .el-step__title{
          color: #a8abb2;
        }
        .el-step__description{
          color: #a8abb2;
        }
      }
    }
  }
}
:deep{
  .case-document{
    padding: 5px 10px;
    background-color: #F0F0FF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
  }
  .danger{
    color: red;
  }
  .m-title{
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #F0F0FF;
    font-size: 14px;
  }
}
</style>
